# app.py
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License.

import sys
import traceback
from datetime import datetime
from query_engine import query_salesforce

from aiohttp import web
from aiohttp.web import Request, Response, json_response
from botbuilder.core import (
    BotFrameworkAdapterSettings,
    TurnContext,
    BotFrameworkAdapter,
)
from botbuilder.core.integration import aiohttp_error_middleware
from botbuilder.schema import Activity, ActivityTypes

from bot import MyBot
from config import DefaultConfig

CONFIG = DefaultConfig()
print(f"[CONFIG] Microsoft App ID: {CONFIG.APP_ID}")
print(f"[CONFIG] Microsoft App Password is {'set' if CONFIG.APP_PASSWORD else 'NOT set'}")

# Create adapter
# For local development, both APP_ID and APP_PASSWORD can be None
SETTINGS = BotFrameworkAdapterSettings(CONFIG.APP_ID, CONFIG.APP_PASSWORD)
ADAPTER = BotFrameworkAdapter(SETTINGS)

# Error handler
async def on_error(context: TurnContext, error: Exception):
    print(f"\n[on_turn_error] unhandled error: {error}", file=sys.stderr)
    traceback.print_exc()

    await context.send_activity("The bot encountered an error or bug.")
    await context.send_activity("To continue to run this bot, please fix the bot source code.")

    if context.activity.channel_id == "emulator":
        try:
            trace_activity = Activity(
                label="TurnError",
                name="on_turn_error Trace",
                timestamp=datetime.utcnow(),
                type=ActivityTypes.trace,
                value=str(error),
                value_type="https://www.botframework.com/schemas/error",
            )
            await context.send_activity(trace_activity)
        except Exception as trace_error:
            print(f"[on_turn_error] Failed to send trace activity: {trace_error}")

ADAPTER.on_turn_error = on_error

# Instantiate your bot
BOT = MyBot()

# Handle POST requests to /api/messages
async def messages(req: Request) -> Response:
    print(f"[CONFIG] Microsoft App ID: {CONFIG.APP_ID or 'None (local development mode)'}")
    print(f"[CONFIG] Microsoft App Password is {'set' if CONFIG.APP_PASSWORD else 'NOT set (local development mode)'}")

    if "application/json" in req.headers.get("Content-Type", ""):
        body = await req.json()
    else:
        return Response(status=415)

    activity = Activity().deserialize(body)

    # 🛠️ Fix: Replace localhost in serviceUrl so emulator is reachable from container
    if "localhost" in activity.service_url:
        activity.service_url = activity.service_url.replace("localhost", "host.docker.internal")

    auth_header = req.headers.get("Authorization", "")
    response = await ADAPTER.process_activity(activity, auth_header, BOT.on_turn)

    if response:
        return json_response(data=response.body, status=response.status)
    return Response(status=201)

# Set up web app and routes
APP = web.Application(middlewares=[aiohttp_error_middleware])
APP.router.add_post("/api/messages", messages)

async def test_query(req: Request) -> Response:
    try:
        question = "Which rep has the most absolute $ growth YOY overall?"  # or take from req.json() optionally
        print(f"[TEST] Running query with question: {question}")
        result = query_salesforce(question)
        return json_response({"status": "success", "result": result})
    except Exception as e:
        print(f"[TEST] Error during query: {e}")
        return json_response({"status": "error", "message": str(e)}, status=500)

APP.router.add_get("/query-test", test_query)

if __name__ == "__main__":
    try:
        print(f"Running on port: {CONFIG.PORT}")
        web.run_app(APP, host="0.0.0.0", port=CONFIG.PORT)
    except Exception as error:
        raise error
