Metadata-Version: 2.1
Name: botbuilder-core
Version: 4.17.0
Summary: Microsoft Bot Framework Bot Builder
Home-page: https://www.github.com/Microsoft/botbuilder-python
Author: Microsoft
License: MIT
Keywords: BotBuilderCore,bots,ai,botframework,botbuilder
Classifier: Programming Language :: Python :: 3.7
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Development Status :: 5 - Production/Stable
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Description-Content-Type: text/x-rst
Requires-Dist: botbuilder-schema==4.17.0
Requires-Dist: botframework-connector==4.17.0
Requires-Dist: botframework-streaming==4.17.0
Requires-Dist: jsonpickle<1.5,>=1.2


==============================
BotBuilder-Core SDK for Python
==============================

.. image:: https://dev.azure.com/FuseLabs/SDK_v4/_apis/build/status/Python/Python-CI-PR-yaml?branchName=master
   :target:  https://dev.azure.com/FuseLabs/SDK_v4/_apis/build/status/Python/Python-CI-PR-yaml?branchName=master
   :align: right
   :alt: Azure DevOps status for master branch
.. image:: https://badge.fury.io/py/botbuilder-core.svg
   :target: https://badge.fury.io/py/botbuilder-core
   :alt: Latest PyPI package version

Within the Bot Framework, BotBuilder-core enables you to build bots that exchange messages with users on channels that are configured in the Bot Framework Portal.

How to Install
==============

.. code-block:: python
  
  pip install botbuilder-core


Documentation/Wiki
==================

You can find more information on the botbuilder-python project by visiting our `Wiki`_.

Requirements
============

* `Python >= 3.7.0`_


Source Code
===========
The latest developer version is available in a github repository:
https://github.com/Microsoft/botbuilder-python/


Contributing
============

This project welcomes contributions and suggestions.  Most contributions require you to agree to a
Contributor License Agreement (CLA) declaring that you have the right to, and actually do, grant us
the rights to use your contribution. For details, visit https://cla.microsoft.com.

When you submit a pull request, a CLA-bot will automatically determine whether you need to provide
a CLA and decorate the PR appropriately (e.g., label, comment). Simply follow the instructions
provided by the bot. You will only need to do this once across all repos using our CLA.

This project has adopted the `Microsoft Open Source Code of Conduct`_.
For more information see the `Code of Conduct FAQ`_ or
contact `<EMAIL>`_ with any additional questions or comments.

Reporting Security Issues
=========================

Security issues and bugs should be reported privately, via email, to the Microsoft Security
Response Center (MSRC) at `<EMAIL>`_. You should
receive a response within 24 hours. If for some reason you do not, please follow up via
email to ensure we received your original message. Further information, including the
`MSRC PGP`_ key, can be found in
the `Security TechCenter`_.

License
=======

Copyright (c) Microsoft Corporation. All rights reserved.

Licensed under the MIT_ License.

.. _Wiki: https://github.com/Microsoft/botbuilder-python/wiki
.. _Python >= 3.7.0: https://www.python.org/downloads/
.. _MIT: https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
.. _Microsoft Open Source Code of Conduct: https://opensource.microsoft.com/codeofconduct/
.. _Code of Conduct FAQ: https://opensource.microsoft.com/codeofconduct/faq/
.. <EMAIL>: mailto:<EMAIL>
.. <EMAIL>: mailto:<EMAIL>
.. _MSRC PGP: https://technet.microsoft.com/en-us/security/dn606155
.. _Security TechCenter: https://github.com/Microsoft/vscode/blob/master/LICENSE.txt

.. <https://technet.microsoft.com/en-us/security/default>`_
