# Credits

## Development Leads

* <PERSON> ([@audreyr](https://github.com/audreyr))
* <PERSON> ([@pydanny](https://github.com/pydanny))
* <PERSON> ([@hackebrot](https://github.com/hackebrot))

## Core Committers

* <PERSON> ([@michael<PERSON><PERSON><PERSON>](https://github.com/michael<PERSON>eph))
* <PERSON> ([@pfmoore](https://github.com/pfmoore))
* <PERSON><PERSON> ([@insspb](https://github.com/insspb))
* <PERSON><PERSON> ([@ssbarnea](https://github.com/ssbarnea))
* Fábio C. Barrionuevo da Luz ([@luzfcb](https://github.com/luzfcb))

## Contributors

* <PERSON> ([@sloria](https://github.com/sloria))
* <PERSON><PERSON> ([@gperetin](https://github.com/gperetin))
* <PERSON><PERSON> ([@foobacca](https://github.com/foobacca))
* <PERSON> ([@krallin](https://github.com/krallin))
* <PERSON>drich Smitka ([@s-m-i-t-a](https://github.com/s-m-i-t-a))
* Benjamin Schwarze ([@benjixx](https://github.com/benjixx))
* Raphi ([@raphigaziano](https://github.com/raphigaziano))
* Thomas Chiroux ([@ThomasChiroux](https://github.com/ThomasChiroux))
* Sergi Almacellas Abellana ([@pokoli](https://github.com/pokoli))
* Alex Gaynor ([@alex](https://github.com/alex))
* Rolo ([@rolo](https://github.com/rolo))
* Pablo ([@oubiga](https://github.com/oubiga))
* Bruno Rocha ([@rochacbruno](https://github.com/rochacbruno))
* Alexander Artemenko ([@svetlyak40wt](https://github.com/svetlyak40wt))
* Mahmoud Abdelkader ([@mahmoudimus](https://github.com/mahmoudimus))
* Leonardo Borges Avelino ([@lborgav](https://github.com/lborgav))
* Chris Trotman ([@solarnz](https://github.com/solarnz))
* Rolf ([@relekang](https://github.com/relekang))
* Noah Kantrowitz ([@coderanger](https://github.com/coderanger))
* Vincent Bernat ([@vincentbernat](https://github.com/vincentbernat))
* Germán Moya ([@pbacterio](https://github.com/pbacterio))
* Ned Batchelder ([@nedbat](https://github.com/nedbat))
* Dave Dash ([@davedash](https://github.com/davedash))
* Johan Charpentier ([@cyberj](https://github.com/cyberj))
* Éric Araujo ([@merwok](https://github.com/merwok))
* saxix ([@saxix](https://github.com/saxix))
* Tzu-ping Chung ([@uranusjr](https://github.com/uranusjr))
* Caleb Hattingh ([@cjrh](https://github.com/cjrh))
* Flavio Curella ([@fcurella](https://github.com/fcurella))
* Adam Venturella ([@aventurella](https://github.com/aventurella))
* Monty Taylor ([@emonty](https://github.com/emonty))
* schacki ([@schacki](https://github.com/schacki))
* Ryan Olson ([@ryanolson](https://github.com/ryanolson))
* Trey Hunner ([@treyhunner](https://github.com/treyhunner))
* Russell Keith-Magee ([@freakboy3742](https://github.com/freakboy3742))
* Mishbah Razzaque ([@mishbahr](https://github.com/mishbahr))
* Robin Andeer ([@robinandeer](https://github.com/robinandeer))
* Rachel Sanders ([@trustrachel](https://github.com/trustrachel))
* Rémy Hubscher ([@Natim](https://github.com/Natim))
* Dino Petron3 ([@dinopetrone](https://github.com/dinopetrone))
* Peter Inglesby ([@inglesp](https://github.com/inglesp))
* Ramiro Batista da Luz ([@ramiroluz](https://github.com/ramiroluz))
* Omer Katz ([@thedrow](https://github.com/thedrow))
* lord63 ([@lord63](https://github.com/lord63))
* Randy Syring ([@rsyring](https://github.com/rsyring))
* Mark Jones ([@mark0978](https://github.com/mark0978))
* Marc Abramowitz ([@msabramo](https://github.com/msabramo))
* Lucian Ursu ([@LucianU](https://github.com/LucianU))
* Osvaldo Santana Neto ([@osantana](https://github.com/osantana))
* Matthias84 ([@Matthias84](https://github.com/Matthias84))
* Simeon Visser ([@svisser](https://github.com/svisser))
* Guruprasad ([@lgp171188](https://github.com/lgp171188))
* Charles-Axel Dein ([@charlax](https://github.com/charlax))
* Diego Garcia ([@drgarcia1986](https://github.com/drgarcia1986))
* maiksensi ([@maiksensi](https://github.com/maiksensi))
* Andrew Conti ([@agconti](https://github.com/agconti))
* Valentin Lab ([@vaab](https://github.com/vaab))
* Ilja Bauer ([@iljabauer](https://github.com/iljabauer))
* Elias Dorneles ([@eliasdorneles](https://github.com/eliasdorneles))
* Matias Saguir ([@mativs](https://github.com/mativs))
* Johannes ([@johtso](https://github.com/johtso))
* macrotim ([@macrotim](https://github.com/macrotim))
* Will McGinnis ([@wdm0006](https://github.com/wdm0006))
* Cédric Krier ([@cedk](https://github.com/cedk))
* Tim Osborn ([@ptim](https://github.com/ptim))
* Aaron Gallagher ([@habnabit](https://github.com/habnabit))
* mozillazg ([@mozillazg](https://github.com/mozillazg))
* Joachim Jablon ([@ewjoachim](https://github.com/ewjoachim))
* Andrew Ittner ([@tephyr](https://github.com/tephyr))
* Diane DeMers Chen ([@purplediane](https://github.com/purplediane))
* zzzirk ([@zzzirk](https://github.com/zzzirk))
* Carol Willing ([@willingc](https://github.com/willingc))
* phoebebauer ([@phoebebauer](https://github.com/phoebebauer))
* Adam Chainz ([@adamchainz](https://github.com/adamchainz))
* Sulé ([@suledev](https://github.com/suledev))
* Evan Palmer ([@palmerev](https://github.com/palmerev))
* Bruce Eckel ([@BruceEckel](https://github.com/BruceEckel))
* Robert Lyon ([@ivanlyon](https://github.com/ivanlyon))
* Terry Bates ([@terryjbates](https://github.com/terryjbates))
* Brett Cannon ([@brettcannon](https://github.com/brettcannon))
* Michael Warkentin ([@mwarkentin](https://github.com/mwarkentin))
* Bartłomiej Kurzeja ([@B3QL](https://github.com/B3QL))
* Thomas O'Donnell ([@andytom](https://github.com/andytom))
* Jeremy Carbaugh ([@jcarbaugh](https://github.com/jcarbaugh))
* Nathan Cheung ([@cheungnj](https://github.com/cheungnj))
* Abdó Roig-Maranges ([@aroig](https://github.com/aroig))
* Steve Piercy ([@stevepiercy](https://github.com/stevepiercy))
* Corey ([@coreysnyder04](https://github.com/coreysnyder04))
* Dmitry Evstratov ([@devstrat](https://github.com/devstrat))
* Eyal Levin ([@eyalev](https://github.com/eyalev))
* mathagician ([@mathagician](https://github.com/mathagician))
* Guillaume Gelin ([@ramnes](https://github.com/ramnes))
* @delirious-lettuce ([@delirious-lettuce](https://github.com/delirious-lettuce))
* Gasper Vozel ([@karantan](https://github.com/karantan))
* Joshua Carp ([@jmcarp](https://github.com/jmcarp))
* @meahow ([@meahow](https://github.com/meahow))
* Andrea Grandi ([@andreagrandi](https://github.com/andreagrandi))
* Issa Jubril ([@jubrilissa](https://github.com/jubrilissa))
* Nytiennzo Madooray ([@Nythiennzo](https://github.com/Nythiennzo))
* Erik Bachorski ([@dornheimer](https://github.com/dornheimer))
* cclauss ([@cclauss](https://github.com/cclauss))
* Andy Craze ([@accraze](https://github.com/accraze))
* Anthony Sottile ([@asottile](https://github.com/asottile))
* Jonathan Sick ([@jonathansick](https://github.com/jonathansick))
* Hugo ([@hugovk](https://github.com/hugovk))
* Min ho Kim ([@minho42](https://github.com/minho42))
* Ryan Ly ([@rly](https://github.com/rly))
* Akintola Rahmat ([@mihrab34](https://github.com/mihrab34))
* Jai Ram Rideout ([@jairideout](https://github.com/jairideout))
* Diego Carrasco Gubernatis ([@dacog](https://github.com/dacog))
* Wagner Negrão ([@wagnernegrao](https://github.com/wagnernegrao))
* Josh Barnes ([@jcb91](https://github.com/jcb91))
* Nikita Sobolev ([@sobolevn](https://github.com/sobolevn))
* Matt Stibbs ([@mattstibbs](https://github.com/mattstibbs))
* MinchinWeb ([@MinchinWeb](https://github.com/MinchinWeb))
* kishan ([@kishan](https://github.com/kishan3))
* tonytheleg ([@tonytheleg](https://github.com/tonytheleg))
