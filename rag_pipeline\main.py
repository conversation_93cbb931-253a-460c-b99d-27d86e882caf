import re
import psycopg2
from openai import AzureOpenAI
from langchain_community.utilities import SQLDatabase
from sqlalchemy import create_engine
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

# Get DB and OpenAI credentials from Azure Key Vault
def get_secrets():
    key_vault_url = "https://ArmstrongAIKeyVault.vault.azure.net"
    credential = DefaultAzureCredential()
    client = SecretClient(vault_url=key_vault_url, credential=credential)

    return {
        "db_user": client.get_secret("db-user").value,
        "db_pass": client.get_secret("db-password").value,
        "db_host": client.get_secret("db-host").value,
        "db_name": client.get_secret("db-name").value,
        "openai_api_key": client.get_secret("openai-api-key").value,
        "openai_endpoint": client.get_secret("openai-endpoint").value,
        "deployment_name": client.get_secret("openai-deployment").value
    }

# Load secrets
secrets = get_secrets()
print("secrets:", secrets)

# Construct SQLAlchemy connection string
conn_str = f"postgresql+psycopg2://{secrets['db_user']}:{secrets['db_pass']}@{secrets['db_host']}/{secrets['db_name']}"
engine = create_engine(conn_str)

# Use LangChain SQLDatabase utility to extract schema info
db = SQLDatabase(engine)
schema_description = db.get_table_info(["product_sales_comparisons"])

# Initialize Azure OpenAI client
client = AzureOpenAI(
    api_key=secrets["openai_api_key"],
    azure_endpoint=secrets["openai_endpoint"],
    api_version="2023-12-01-preview"  # or whatever version you're using
)
deployment_name = secrets["deployment_name"]

# User's natural language question
question = "What are our top 5 performing reps (Sales Volume YTD) in Hot Water"
print("\nNatural Language Query:\n", question)

# Build schema-aware prompt using live DB structure
prompt = f"""
You are a helpful assistant that translates natural language questions into SQL queries.

Here is the database schema:
{schema_description}

Column mappings:
- reseller_account → Reseller Account, rep, representative, sales rep
- product_category_description → product, product category, category, line of business

Language Guidance:
- "absolute" or "overall" means aggregate across all `product_category_description` (i.e., sum across categories)
- When a product name is mentioned (e.g., "Hot Water", "Steam"), filter using `product_category_description`

Translate the following question into a valid SQL query:
\"\"\"{question}\"\"\"
Only return the SQL statement, nothing else.
"""

# Call Azure OpenAI to generate SQL
response = client.chat.completions.create(
    model=deployment_name,
    messages=[
        {"role": "system", "content": "You are a SQL expert."},
        {"role": "user", "content": prompt}
    ],
    temperature=0,
    max_tokens=500
)

# Clean up markdown formatting from LLM output
raw_sql = response.choices[0].message.content
generated_sql = re.sub(r"```(?:sql)?|```", "", raw_sql).strip()

print("\nGenerated SQL:\n", generated_sql)

# Connect to PostgreSQL and execute the query
try:
    conn = psycopg2.connect(
        host=secrets["db_host"],
        database=secrets["db_name"],
        user=secrets["db_user"],
        password=secrets["db_pass"]
    )
    cur = conn.cursor()

    cur.execute(generated_sql)
    rows = cur.fetchall()

    print("\nQuery Results:")
    for row in rows:
        print(row)

except Exception as e:
    print("\n❌ Error executing SQL:", e)

finally:
    if 'cur' in locals(): cur.close()
    if 'conn' in locals(): conn.close()
