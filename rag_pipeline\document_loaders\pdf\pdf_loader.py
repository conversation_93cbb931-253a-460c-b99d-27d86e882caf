import re
from pathlib import <PERSON>
from typing import List, Tuple
from pypdf import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_community.embeddings import OllamaEmbeddings
from deepseek_tokenizer import ds_token


def load_pdf_text(pdf_path: str) -> str:
    """Loads raw text from a PDF file and removes excessive blank lines."""
    print(f"Loading PDF: {pdf_path}")
    reader = PdfReader(str(pdf_path))
    text_parts = []
    page_count = len(reader.pages)
    print(f"PDF has {page_count} pages")

    for i, page in enumerate(reader.pages):
        page_text = page.extract_text() or ""
        text_parts.append(page_text)
        print(f"  Page {i+1}/{page_count}: Extracted {len(page_text)} characters")

    full_text = "\n".join(text_parts)
    cleaned_text = re.sub(r'\n\s*\n+', '\n\n', full_text)

    return cleaned_text


def chunk_text(text: str, max_tokens: int = 512, overlap: int = 64) -> List[str]:
    """Chunks text into overlapping segments using Deepseek tokenizer."""
    print(f"Chunking text with max_tokens={max_tokens}, overlap={overlap}")
    tokens = ds_token.encode(text)
    print(f"Text encoded to {len(tokens)} tokens")

    chunks = []
    start = 0
    while start < len(tokens):
        end = min(start + max_tokens, len(tokens))
        chunk = ds_token.decode(tokens[start:end])
        chunks.append(chunk)
        print(f"  Created chunk {len(chunks)}: {start}-{end} ({end-start} tokens, {len(chunk)} chars)")
        start += max_tokens - overlap

    print(f"Created {len(chunks)} chunks in total")
    return chunks


def prepare_for_hybrid_storage(pdf_path: str, max_tokens: int = 512, overlap: int = 64):
    from pypdf import PdfReader
    from pathlib import Path
    import re
    from langchain_community.embeddings import OllamaEmbeddings
    from deepseek_tokenizer import ds_token

    print(f"\n{'='*50}\nProcessing {pdf_path} for hybrid storage\n{'='*50}")
    embedding_model = OllamaEmbeddings(
        model="nomic-embed-text:latest",
        base_url="http://localhost:11434"
    )

    reader = PdfReader(pdf_path)
    file_name = Path(pdf_path).name
    all_chunks = []
    all_metadata = []

    for page_num, page in enumerate(reader.pages, start=1):
        raw_text = page.extract_text() or ""
        cleaned_text = re.sub(r'\n\s*\n+', '\n\n', raw_text)
        page_chunks = chunk_text(cleaned_text, max_tokens, overlap)

        for i, chunk in enumerate(page_chunks):
            all_chunks.append(chunk)
            all_metadata.append({
                "file_path": str(pdf_path),
                "file_name": file_name,
                "chunk_id": len(all_metadata),
                "chunk_text": chunk,
                "embedding_id": len(all_metadata),
                "page_number": page_num if page_num is not None else -1
            })

    print(f"Generating embeddings for {len(all_chunks)} chunks...")
    try:
        embeddings = embedding_model.embed_documents(all_chunks)
    except Exception as e:
        print(f"Error generating embeddings: {e}")
        return [], []

    return all_metadata, embeddings

    """Prepares PDF content for hybrid storage (PostgreSQL + FAISS)."""
    print(f"\n{'='*50}\nProcessing {pdf_path} for hybrid storage\n{'='*50}")
    embedding_model = OllamaEmbeddings(
        model="nomic-embed-text:latest",
        base_url="http://localhost:11434"
    )

    try:
        text = load_pdf_text(pdf_path)
    except Exception as e:
        print(f"Error loading PDF: {e}")
        return [], []

    chunks = chunk_text(text, max_tokens, overlap)
    file_name = Path(pdf_path).name
    print(f"Generating embeddings for {len(chunks)} chunks...")

    try:
        embeddings = embedding_model.embed_documents(chunks)
    except Exception as e:
        print(f"Error generating embeddings: {e}")
        return [], []

    print(f"Generated {len(embeddings)} embeddings")

    document_records = []
    for i, chunk in enumerate(chunks):
        document_records.append({
            "file_path": str(pdf_path),
            "file_name": file_name,
            "chunk_id": i,
            "chunk_text": chunk,
            "embedding_id": i
        })

    print(f"Created {len(document_records)} document records")
    return document_records, embeddings


def load_all_pdf_paths(base_dir: str) -> List[Path]:
    """Recursively finds all PDF files under the given base directory."""
    base_path = Path(base_dir)
    pdf_paths = list(base_path.rglob("*.pdf"))
    print(f"Found {len(pdf_paths)} PDF files in {base_dir}")
    return pdf_paths


def process_directory_for_hybrid_storage(base_dir: str, max_tokens: int = 512, overlap: int = 64):
    """Processes all PDFs in a directory tree one-by-one for hybrid storage."""
    pdf_paths = load_all_pdf_paths(base_dir)
    for pdf_path in pdf_paths:
        records, embeddings = prepare_for_hybrid_storage(str(pdf_path), max_tokens, overlap)
        # You can now save `records` and `embeddings` to PostgreSQL and FAISS respectively.
        # Example: save_to_postgres(records), save_to_faiss(embeddings)
        print(f"Processed {pdf_path} ✅\n")
