import os
import json
import re
import psycopg2
from openai import AzureOpenAI
from dotenv import load_dotenv
from langchain_community.utilities import SQLDatabase
from sqlalchemy import create_engine

# Load environment variables from .env
load_dotenv()

# Load DB credentials from db_config.json
with open("db_config.json", "r") as f:
    db_config = json.load(f)

# Construct SQLAlchemy connection string
conn_str = f"postgresql+psycopg2://{db_config['user']}:{db_config['password']}@{db_config['host']}/{db_config['name']}"
engine = create_engine(conn_str)

# Use LangChain SQLDatabase utility to extract schema info
db = SQLDatabase(engine)
schema_description = db.get_table_info(["product_sales_comparisons"])
#print("\Schema Description:\n", schema_description)

# Initialize Azure OpenAI client
client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION")
)
deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT")

# User's natural language question
question = "What are our top 5 performing reps (Sales Volume YTD) in Hot Water"
print("\nNatural Language Query:\n", question)

# Build schema-aware prompt using live DB structure
prompt = f"""
You are a helpful assistant that translates natural language questions into SQL queries.

Here is the database schema:
{schema_description}

Column mappings:
- reseller_account → Reseller Account, rep, representative, sales rep
- product_category_description → product, product category, category, line of business

Language Guidance:
- "absolute" or "overall" means aggregate across all `product_category_description` (i.e., sum across categories)
- When a product name is mentioned (e.g., "Hot Water", "Steam"), filter using `product_category_description`

Translate the following question into a valid SQL query:
\"\"\"{question}\"\"\"
Only return the SQL statement, nothing else.
"""

# Call Azure OpenAI to generate SQL
response = client.chat.completions.create(
    model=deployment_name,
    messages=[
        {"role": "system", "content": "You are a SQL expert."},
        {"role": "user", "content": prompt}
    ],
    temperature=0,
    max_tokens=500
)

# Clean up markdown formatting from LLM output
raw_sql = response.choices[0].message.content
generated_sql = re.sub(r"```(?:sql)?|```", "", raw_sql).strip()

print("\nGenerated SQL:\n", generated_sql)

# Connect to PostgreSQL and execute the query
conn = psycopg2.connect(
    host=db_config["host"],
    database=db_config["name"],
    user=db_config["user"],
    password=db_config["password"]
)
cur = conn.cursor()

try:
    cur.execute(generated_sql)
    rows = cur.fetchall()

    print("\nQuery Results:")
    for row in rows:
        print(row)

except Exception as e:
    print("\n❌ Error executing SQL:", e)

finally:
    cur.close()
    conn.close()
