botframework/streaming/__init__.py,sha256=fS0gj462O-jExmr078s040PpfYv9rCkBiVwDDz5616g,900
botframework/streaming/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/__pycache__/about.cpython-313.pyc,,
botframework/streaming/__pycache__/payload_stream.cpython-313.pyc,,
botframework/streaming/__pycache__/protocol_adapter.cpython-313.pyc,,
botframework/streaming/__pycache__/receive_request.cpython-313.pyc,,
botframework/streaming/__pycache__/receive_response.cpython-313.pyc,,
botframework/streaming/__pycache__/request_handler.cpython-313.pyc,,
botframework/streaming/__pycache__/streaming_request.cpython-313.pyc,,
botframework/streaming/__pycache__/streaming_response.cpython-313.pyc,,
botframework/streaming/about.py,sha256=xT0RjgAmBDzrnn9azWj4N90GvAxRRTreYph-npLLLyE,474
botframework/streaming/payload_stream.py,sha256=79kpFAQIWghIjWTObLpeGHm2FZQqhrCg16hdfkQY9Y8,2328
botframework/streaming/payload_transport/__init__.py,sha256=zfY7YbxkIBxBc77TCoyaEUMBI8eJotgTMEZ6_KXikzg,283
botframework/streaming/payload_transport/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/payload_transport/__pycache__/payload_receiver.cpython-313.pyc,,
botframework/streaming/payload_transport/__pycache__/payload_sender.cpython-313.pyc,,
botframework/streaming/payload_transport/__pycache__/send_packet.cpython-313.pyc,,
botframework/streaming/payload_transport/__pycache__/send_queue.cpython-313.pyc,,
botframework/streaming/payload_transport/payload_receiver.py,sha256=YSM1bYSwfguDU6pVfYsMLRx-7XLYX_EJsqOLAUm3u6k,6391
botframework/streaming/payload_transport/payload_sender.py,sha256=WPHpLBjSDQWhMtCz_Dz8xCZ51eeaHa-D9fLtuCAkkd0,5895
botframework/streaming/payload_transport/send_packet.py,sha256=U_UTMA28RmmNqh36n8CqrOJCfp9lHqL0x8Mwzu_7MV8,546
botframework/streaming/payload_transport/send_queue.py,sha256=UgjgcbepbzXR5HXY_FZZS3xqJeDqnp5-N_Wn1R9Z6Fg,1183
botframework/streaming/payloads/__init__.py,sha256=TgJgXPpMlWBQ1JHUIht9v9JsODWCeRFewagsUfIL74w,622
botframework/streaming/payloads/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/payloads/__pycache__/content_stream.cpython-313.pyc,,
botframework/streaming/payloads/__pycache__/header_serializer.cpython-313.pyc,,
botframework/streaming/payloads/__pycache__/payload_assembler_manager.cpython-313.pyc,,
botframework/streaming/payloads/__pycache__/request_manager.cpython-313.pyc,,
botframework/streaming/payloads/__pycache__/response_message_stream.cpython-313.pyc,,
botframework/streaming/payloads/__pycache__/send_operations.cpython-313.pyc,,
botframework/streaming/payloads/__pycache__/stream_manager.cpython-313.pyc,,
botframework/streaming/payloads/assemblers/__init__.py,sha256=zvHrBe6wXZwlwjyLe125w6BhRwCXeyoOpMshEiZinqY,442
botframework/streaming/payloads/assemblers/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/payloads/assemblers/__pycache__/assembler.cpython-313.pyc,,
botframework/streaming/payloads/assemblers/__pycache__/payload_stream_assembler.cpython-313.pyc,,
botframework/streaming/payloads/assemblers/__pycache__/receive_request_assembler.cpython-313.pyc,,
botframework/streaming/payloads/assemblers/__pycache__/receive_response_assembler.cpython-313.pyc,,
botframework/streaming/payloads/assemblers/assembler.py,sha256=SDC8D-gKx2WQpvbIHCi7faIFl8OGkFYWkC4-0hK4Scs,742
botframework/streaming/payloads/assemblers/payload_stream_assembler.py,sha256=jKsqKcfuM0f7E7iLyOeOe4QFHGBGVbLTud_lQXrGXyg,1444
botframework/streaming/payloads/assemblers/receive_request_assembler.py,sha256=3-ycZcAuwmH7W_E5Ib1Eap-GTcYHlMjEq_WNLR0sp8w,3076
botframework/streaming/payloads/assemblers/receive_response_assembler.py,sha256=7gQfoMaGv1j0uOB3ozhoF8Dxtu4gOJieWPv0a8u7uF0,3228
botframework/streaming/payloads/content_stream.py,sha256=vnVdmx-XwfUDAr63XDy0PYkIz49GR5EwDpJOcI1kRkw,712
botframework/streaming/payloads/disassemblers/__init__.py,sha256=BcPI4CfXBGqdD0Fb_7WUeQOQ1eFZ-vjR9wxDBq7SMac,559
botframework/streaming/payloads/disassemblers/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/payloads/disassemblers/__pycache__/cancel_disassembler.cpython-313.pyc,,
botframework/streaming/payloads/disassemblers/__pycache__/payload_disassembler.cpython-313.pyc,,
botframework/streaming/payloads/disassemblers/__pycache__/request_disassembler.cpython-313.pyc,,
botframework/streaming/payloads/disassemblers/__pycache__/response_disassembler.cpython-313.pyc,,
botframework/streaming/payloads/disassemblers/__pycache__/response_message_stream_disassembler.cpython-313.pyc,,
botframework/streaming/payloads/disassemblers/cancel_disassembler.py,sha256=Ap5x6ldNu1mpqPhh92YJPU4TPAHM2hexm00TgUKVVJk,661
botframework/streaming/payloads/disassemblers/payload_disassembler.py,sha256=RK22PpS03tzY7tZ2i52yFHY8NwGAXxobQW-keqVJUDs,3566
botframework/streaming/payloads/disassemblers/request_disassembler.py,sha256=shD5OcnYSRZ7gugGF0bPkJ9z689ja2WkGTqj9Xa5wsQ,1251
botframework/streaming/payloads/disassemblers/response_disassembler.py,sha256=P5JSZc0nso0wlNLEb-Shr-Pn1BbXyEFYSHVP0UBpJRQ,1252
botframework/streaming/payloads/disassemblers/response_message_stream_disassembler.py,sha256=qs_LtJKzQdRKxChU117hhin14eGON9xOsqDpwr5BY94,973
botframework/streaming/payloads/header_serializer.py,sha256=P-dv-VFKTBo6TvR1twU14ZXy0mprR9rgXsWSx2YisTU,5564
botframework/streaming/payloads/models/__init__.py,sha256=Oo5J3hEi8ddjBkGaeS6NSEdnwmQcMpsMI3uxhZgHT1I,481
botframework/streaming/payloads/models/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/payloads/models/__pycache__/header.cpython-313.pyc,,
botframework/streaming/payloads/models/__pycache__/payload_types.cpython-313.pyc,,
botframework/streaming/payloads/models/__pycache__/request_payload.cpython-313.pyc,,
botframework/streaming/payloads/models/__pycache__/response_payload.cpython-313.pyc,,
botframework/streaming/payloads/models/__pycache__/serializable.cpython-313.pyc,,
botframework/streaming/payloads/models/__pycache__/stream_description.cpython-313.pyc,,
botframework/streaming/payloads/models/header.py,sha256=XFYBwWITcH1GHa6vEtcGNhzY_vwyjnMWSZFmoRuqG7s,1065
botframework/streaming/payloads/models/payload_types.py,sha256=YZz-qOtLxB0Xa3ah7mT57MCCNGP3X817AZDRkHVitsM,355
botframework/streaming/payloads/models/request_payload.py,sha256=Mfi8FOQSke7N8XLQZCDa0wKS3Zg-cpIAvEav13M5HTU,1112
botframework/streaming/payloads/models/response_payload.py,sha256=wWy6oH2e9zJw42n0w6rCOWFg4All4qMR5gTYKYPfVGo,1031
botframework/streaming/payloads/models/serializable.py,sha256=oSUUxoOCM9BdNKZwF-6CZeRlED5Z7MZhzPDBGAZ6ljU,364
botframework/streaming/payloads/models/stream_description.py,sha256=S9o1AGkJTt9LAIISbA2g7fCl0Uj6st5kcTONLip79gM,1015
botframework/streaming/payloads/payload_assembler_manager.py,sha256=6oI34Qe-F8jgKYwfqPGUncQSisTS3D0eOc1VjdFMuyw,2776
botframework/streaming/payloads/request_manager.py,sha256=9z6C28f_OkMGSmhM9eFfNwY15qWdl4YFMEfbT_873AY,1296
botframework/streaming/payloads/response_message_stream.py,sha256=W1kzMCHanOiU4LawviKUEUEm5cvauqYawNgpLtZmIT0,321
botframework/streaming/payloads/send_operations.py,sha256=jzK-Q5TbzN66tp8n5jx4UpkiEvZ6OvgyLDPc3DHKo9w,2186
botframework/streaming/payloads/stream_manager.py,sha256=rme_SNgC1k7-xjGJ_tf1060Q5x0vhHzEq48VMm-jB_c,1755
botframework/streaming/protocol_adapter.py,sha256=2PKN9owseyuJu_nODuKx4GoXpL-_HlDO2j44FV_isy8,3088
botframework/streaming/receive_request.py,sha256=QzrO8ZE0eF0VQ5qsYH7Pu4bDNj9wHGjz5vGDzqkBQuM,882
botframework/streaming/receive_response.py,sha256=J8is2Oz8hnvg_GXRhFUdnkH6TlUalloaK8lW5ww9tSM,1557
botframework/streaming/request_handler.py,sha256=hyy2M3YvIegDrmes0bKVuxLnAhyxT8Htv7VPPTm19dQ,432
botframework/streaming/streaming_request.py,sha256=wiTQ3nWQLNh4EjpRL9dF3UFhWW2VJw6fhwLSS0YCy4M,2588
botframework/streaming/streaming_response.py,sha256=ao9Bdez75FKuTGDnlJRC5pDB6LtPIfSpUI8sPwqt43M,2227
botframework/streaming/transport/__init__.py,sha256=g8AjMgEfdSFWszM1Pbb4ylBYaUk7pYgIQKzlWSMJHcQ,609
botframework/streaming/transport/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/transport/__pycache__/disconnected_event_args.cpython-313.pyc,,
botframework/streaming/transport/__pycache__/streaming_transport_service.cpython-313.pyc,,
botframework/streaming/transport/__pycache__/transport_base.cpython-313.pyc,,
botframework/streaming/transport/__pycache__/transport_constants.cpython-313.pyc,,
botframework/streaming/transport/__pycache__/transport_receiver_base.cpython-313.pyc,,
botframework/streaming/transport/__pycache__/transport_sender_base.cpython-313.pyc,,
botframework/streaming/transport/disconnected_event_args.py,sha256=HEG8JkJ2bA1ExdFFp9zSKykc_E9wIn1t8GXd7HKUuGs,257
botframework/streaming/transport/streaming_transport_service.py,sha256=xVPPh9zipv_0f7Mi1TPscySQB_ghCbZG-5myT5XoTXQ,290
botframework/streaming/transport/transport_base.py,sha256=5mqkZEkNgKqmcrwXrtUr_TF7FyeLsVwO6CsSRV7JjVE,217
botframework/streaming/transport/transport_constants.py,sha256=AOgrceN1XtVsMGBArCSo_C2eQul1K1SelXFhnqA2hzQ,248
botframework/streaming/transport/transport_receiver_base.py,sha256=nr8FKoNN2cG91x2dubqAVLcVizHxjLGepNeRqnhZA9U,322
botframework/streaming/transport/transport_sender_base.py,sha256=UN9Ke-Sf-GIJadvYTSGWqPdJWDX1jF7-8NtpvGLS0Vs,317
botframework/streaming/transport/web_socket/__init__.py,sha256=IvkK4s-Zv9jpxfPl2p_Na4_01iE0qkEfo1ujoAdqd6A,614
botframework/streaming/transport/web_socket/__pycache__/__init__.cpython-313.pyc,,
botframework/streaming/transport/web_socket/__pycache__/web_socket.cpython-313.pyc,,
botframework/streaming/transport/web_socket/__pycache__/web_socket_close_status.cpython-313.pyc,,
botframework/streaming/transport/web_socket/__pycache__/web_socket_message_type.cpython-313.pyc,,
botframework/streaming/transport/web_socket/__pycache__/web_socket_server.cpython-313.pyc,,
botframework/streaming/transport/web_socket/__pycache__/web_socket_state.cpython-313.pyc,,
botframework/streaming/transport/web_socket/__pycache__/web_socket_transport.cpython-313.pyc,,
botframework/streaming/transport/web_socket/web_socket.py,sha256=HRQ_EFVi7zKMezAe5XCzGnQOF9itNlhrG3PIzbnF2S8,1011
botframework/streaming/transport/web_socket/web_socket_close_status.py,sha256=GJGIIsheFOP6UpcLmU0GDPPqXWr9b4y7ibIkRbUlBk0,443
botframework/streaming/transport/web_socket/web_socket_message_type.py,sha256=2ZPvzAl7HOSnV2eHn2wwsxFEibf4PIYO8hBC7-N8sbs,276
botframework/streaming/transport/web_socket/web_socket_server.py,sha256=GYD1EUH_QaKrS0q6wY8t13ZmRY9I8ZLG0sdZRBU7M1s,3525
botframework/streaming/transport/web_socket/web_socket_state.py,sha256=TcH-Au6cgWqKeZSQ44VTkz3PhwbVr-9gXoDHKIWPH4k,181
botframework/streaming/transport/web_socket/web_socket_transport.py,sha256=771HOlU7DDGi8twMhL9sSoVrwDWnqJ5dSFXexIFYaN0,3920
botframework_streaming-4.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
botframework_streaming-4.17.0.dist-info/METADATA,sha256=_oRNa7JacMreGLPx6avWxi-LOoCF70mIXKvlGmJovI4,3751
botframework_streaming-4.17.0.dist-info/RECORD,,
botframework_streaming-4.17.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
botframework_streaming-4.17.0.dist-info/WHEEL,sha256=R0nc6qTxuoLk7ShA2_Y-UWkN8ZdfDBG2B6Eqpz2WXbs,91
botframework_streaming-4.17.0.dist-info/top_level.txt,sha256=dpCBSDjo9su5dmQYbevD9jc_rxSry_Vb78_gI_wn9Bc,13
