import os

class DefaultConfig:
    """ Bot Configuration """

    PORT = int(os.environ.get("PORT", 3978))  # Default to 3978 locally, use Azure's PORT if set

    # Bot credentials
    APP_ID = os.environ.get("MicrosoftAppId", None)
    APP_PASSWORD = os.environ.get("MicrosoftAppPassword", None)

    # For User-Assigned Managed Identity
    # Set this to True when using managed identity in Azure
    USE_MANAGED_IDENTITY = os.environ.get("USE_MANAGED_IDENTITY", "false").lower() == "true"

    # Bot Framework authentication type
    # For User-Assigned Managed Identity, this should be "UserAssignedMSI"
    APP_TYPE = os.environ.get("MicrosoftAppType", "UserAssignedMSI")

    # Tenant ID for single-tenant or managed identity scenarios
    APP_TENANT_ID = os.environ.get("MicrosoftAppTenantId", None)
