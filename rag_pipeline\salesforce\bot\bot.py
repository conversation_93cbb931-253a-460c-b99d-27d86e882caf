# bot.py

from botbuilder.core import ActivityHandler, TurnContext
from botbuilder.schema import ChannelAccount
from query_engine import query_salesforce  # custom query logic

class MyBot(ActivityHandler):
    async def on_message_activity(self, turn_context: TurnContext):
        user_question = turn_context.activity.text
        result = query_salesforce(user_question)
        await turn_context.send_activity(result)

    async def on_members_added_activity(self, members_added, turn_context: TurnContext):
        for member in members_added:
            if member.id != turn_context.activity.recipient.id:
                await turn_context.send_activity("Welcome to the Armstrong Sales Insights Bot!")
