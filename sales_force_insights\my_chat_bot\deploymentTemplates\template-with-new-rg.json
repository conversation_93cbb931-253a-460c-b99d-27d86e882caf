{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"groupLocation": {"type": "string", "metadata": {"description": "Specifies the location of the Resource Group."}}, "groupName": {"type": "string", "metadata": {"description": "Specifies the name of the Resource Group."}}, "appId": {"type": "string", "metadata": {"description": "Active Directory App ID, set as MicrosoftAppId in the Web App's Application Settings."}}, "appSecret": {"type": "string", "metadata": {"description": "Active Directory App Password, set as MicrosoftAppPassword in the Web App's Application Settings."}}, "botId": {"type": "string", "metadata": {"description": "The globally unique and immutable bot ID. Also used to configure the displayName of the bot, which is mutable."}}, "botSku": {"type": "string", "metadata": {"description": "The pricing tier of the Bot Service Registration. Acceptable values are F0 and S1."}}, "newAppServicePlanName": {"type": "string", "metadata": {"description": "The name of the App Service Plan."}}, "newAppServicePlanSku": {"type": "object", "defaultValue": {"name": "S1", "tier": "Standard", "size": "S1", "family": "S", "capacity": 1}, "metadata": {"description": "The SKU of the App Service Plan. Defaults to Standard values."}}, "newAppServicePlanLocation": {"type": "string", "metadata": {"description": "The location of the App Service Plan. Defaults to \"westus\"."}}, "newWebAppName": {"type": "string", "defaultValue": "", "metadata": {"description": "The globally unique name of the Web App. Defaults to the value passed in for \"botId\"."}}}, "variables": {"appServicePlanName": "[parameters('newAppServicePlanName')]", "resourcesLocation": "[parameters('newAppServicePlanLocation')]", "webAppName": "[if(empty(parameters('newWebAppName')), parameters('botId'), parameters('newWebAppName'))]", "siteHost": "[concat(variables('webAppName'), '.azurewebsites.net')]", "botEndpoint": "[concat('https://', variables('siteHost'), '/api/messages')]", "publishingUsername": "[concat('$', parameters('newWebAppName'))]", "resourceGroupId": "[concat(subscription().id, '/resourceGroups/', parameters('groupName'))]"}, "resources": [{"name": "[parameters('groupName')]", "type": "Microsoft.Resources/resourceGroups", "apiVersion": "2018-05-01", "location": "[parameters('groupLocation')]", "properties": {}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2018-05-01", "name": "storageDeployment", "resourceGroup": "[parameters('groupName')]", "dependsOn": ["[resourceId('Microsoft.Resources/resourceGroups/', parameters('groupName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"comments": "Create a new Linux App Service Plan if no existing App Service Plan name was passed in.", "type": "Microsoft.Web/serverfarms", "name": "[variables('appServicePlanName')]", "apiVersion": "2018-02-01", "location": "[variables('resourcesLocation')]", "sku": "[parameters('newAppServicePlanSku')]", "kind": "linux", "properties": {"name": "[variables('appServicePlanName')]", "perSiteScaling": false, "reserved": true, "targetWorkerCount": 0, "targetWorkerSizeId": 0}}, {"comments": "Create a Web App using a Linux App Service Plan", "type": "Microsoft.Web/sites", "apiVersion": "2015-08-01", "location": "[variables('resourcesLocation')]", "kind": "app,linux", "dependsOn": ["[concat(variables('resourceGroupId'), '/providers/Microsoft.Web/serverfarms/', variables('appServicePlanName'))]"], "name": "[variables('webAppName')]", "properties": {"name": "[variables('webAppName')]", "hostNameSslStates": [{"name": "[concat(parameters('newWebAppName'), '.azurewebsites.net')]", "sslState": "Disabled", "hostType": "Standard"}, {"name": "[concat(parameters('newWebAppName'), '.scm.azurewebsites.net')]", "sslState": "Disabled", "hostType": "Repository"}], "serverFarmId": "[variables('appServicePlanName')]", "siteConfig": {"appSettings": [{"name": "SCM_DO_BUILD_DURING_DEPLOYMENT", "value": "true"}, {"name": "MicrosoftAppId", "value": "[parameters('appId')]"}, {"name": "MicrosoftAppPassword", "value": "[parameters('appSecret')]"}], "cors": {"allowedOrigins": ["https://botservice.hosting.portal.azure.net", "https://hosting.onecloud.azure-test.net/"]}}}}, {"type": "Microsoft.Web/sites/config", "apiVersion": "2016-08-01", "name": "[concat(variables('webAppName'), '/web')]", "location": "[variables('resourcesLocation')]", "dependsOn": ["[concat(variables('resourceGroupId'), '/providers/Microsoft.Web/sites/', variables('webAppName'))]"], "properties": {"numberOfWorkers": 1, "defaultDocuments": ["Default.htm", "Default.html", "Default.asp", "index.htm", "index.html", "iisstart.htm", "default.aspx", "index.php", "hostingstart.html"], "netFrameworkVersion": "v4.0", "phpVersion": "", "pythonVersion": "", "nodeVersion": "", "linuxFxVersion": "PYTHON|3.7", "requestTracingEnabled": false, "remoteDebuggingEnabled": false, "remoteDebuggingVersion": "VS2017", "httpLoggingEnabled": true, "logsDirectorySizeLimit": 35, "detailedErrorLoggingEnabled": false, "publishingUsername": "[variables('publishingUsername')]", "scmType": "None", "use32BitWorkerProcess": true, "webSocketsEnabled": false, "alwaysOn": false, "appCommandLine": "gunicorn --bind 0.0.0.0 --worker-class aiohttp.worker.GunicornWebWorker --timeout 600 app:APP", "managedPipelineMode": "Integrated", "virtualApplications": [{"virtualPath": "/", "physicalPath": "site\\wwwroot", "preloadEnabled": false, "virtualDirectories": null}], "winAuthAdminState": 0, "winAuthTenantState": 0, "customAppPoolIdentityAdminState": false, "customAppPoolIdentityTenantState": false, "loadBalancing": "LeastRequests", "routingRules": [], "experiments": {"rampUpRules": []}, "autoHealEnabled": false, "vnetName": "", "minTlsVersion": "1.2", "ftpsState": "AllAllowed", "reservedInstanceCount": 0}}, {"apiVersion": "2021-03-01", "type": "Microsoft.BotService/botServices", "name": "[parameters('botId')]", "location": "global", "kind": "azurebot", "sku": {"name": "[parameters('botSku')]"}, "properties": {"name": "[parameters('botId')]", "displayName": "[parameters('botId')]", "iconUrl": "https://docs.botframework.com/static/devportal/client/images/bot-framework-default.png", "endpoint": "[variables('botEndpoint')]", "msaAppId": "[parameters('appId')]", "luisAppIds": [], "schemaTransformationVersion": "1.3", "isCmekEnabled": false, "isIsolated": false}, "dependsOn": ["[concat(variables('resourceGroupId'), '/providers/Microsoft.Web/sites/', variables('webAppName'))]"]}], "outputs": {}}}}]}