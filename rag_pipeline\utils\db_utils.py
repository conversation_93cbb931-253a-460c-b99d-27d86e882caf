"""Database utility functions for the RAG pipeline."""
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Dict, Any, List, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from contextlib import contextmanager

def load_db_config(config_path: str) -> Dict[str, str]:
    """Load database configuration from a JSON file."""
    print(f"Loading database config from {config_path}")
    with open(config_path, 'r') as f:
        db_config = json.load(f)
    return db_config

def get_connection_string(config: Dict[str, str]) -> str:
    """Create a PostgreSQL connection string from config."""
    return f"postgresql://{config['user']}:{config['password']}@{config['host']}/{config['database']}"

def create_db_engine(config_path: str) -> Engine:
    """Create a SQLAlchemy engine from config file."""
    config = load_db_config(config_path)
    connection_string = get_connection_string(config)
    print(f"Creating SQLAlchemy engine for {config['host']}")
    return create_engine(connection_string)

def connect_to_db(config_path: str):
    """Connect to PostgreSQL database using psycopg2."""
    config = load_db_config(config_path)
    
    print(f"Connecting to PostgreSQL at {config['host']}")
    conn = psycopg2.connect(
        host=config["host"],
        database=config["database"],
        user=config["user"],
        password=config["password"]
    )
    print("Database connection established")
    return conn

@contextmanager
def get_db_connection(config_path: str):
    """Context manager for database connections."""
    conn = None
    try:
        conn = connect_to_db(config_path)
        yield conn
    finally:
        if conn:
            conn.close()
            print("Database connection closed")

@contextmanager
def get_db_cursor(config_path: str, cursor_factory=None):
    """Context manager for database cursors."""
    with get_db_connection(config_path) as conn:
        cursor = conn.cursor(cursor_factory=cursor_factory)
        try:
            yield cursor
        finally:
            cursor.close()

def execute_query(config_path: str, query: str, params: Optional[tuple] = None, 
                 fetch_one: bool = False, dict_cursor: bool = False) -> List[Dict[str, Any]]:
    """Execute a query and return results."""
    cursor_factory = RealDictCursor if dict_cursor else None
    
    with get_db_cursor(config_path, cursor_factory) as cursor:
        cursor.execute(query, params or ())
        
        if fetch_one:
            result = cursor.fetchone()
            return [result] if result else []
        else:
            return cursor.fetchall()

def fetch_document_by_embedding_id(config_path: str, embedding_id: int) -> Dict[str, Any]:
    """Fetch a document chunk by its embedding ID."""
    query = """
        SELECT file_path, file_name, chunk_id, chunk_text, page_number
        FROM document_chunks 
        WHERE embedding_id = %s
    """
    results = execute_query(config_path, query, (embedding_id,), fetch_one=True, dict_cursor=True)
    return results[0] if results else None

def fetch_documents_by_embedding_ids(config_path: str, embedding_ids: List[int]) -> List[Dict[str, Any]]:
    """Fetch multiple document chunks by their embedding IDs."""
    placeholders = ','.join(['%s'] * len(embedding_ids))
    query = f"""
        SELECT file_path, file_name, chunk_id, chunk_text, page_number
        FROM document_chunks 
        WHERE embedding_id IN ({placeholders})
        ORDER BY ARRAY_POSITION(ARRAY[{placeholders}], embedding_id)
    """
    # We need to pass the embedding_ids twice - once for the IN clause and once for the ARRAY_POSITION
    params = embedding_ids + embedding_ids
    return execute_query(config_path, query, params, dict_cursor=True)