import os
import argparse
from pathlib import Path
import json
import numpy as np
import logging
from tqdm import tqdm
import psycopg2
import faiss  # Ensure faiss-cpu or faiss-gpu is installed
from document_loaders.pdf.pdf_loader_unstructured import prepare_for_hybrid_storage

def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser(description="Process PDFs for RAG pipeline")
    parser.add_argument("--pdf_dir", type=str, required=True, help="Directory containing PDF files")
    parser.add_argument("--db_config", type=str, required=True, help="Path to database config JSON file")
    parser.add_argument("--faiss_index", type=str, required=True, help="Path to save FAISS index")
    parser.add_argument("--max_tokens", type=int, default=512, help="Maximum tokens per chunk")
    parser.add_argument("--overlap", type=int, default=64, help="Overlap tokens between chunks")
    args = parser.parse_args()

    # Load database config
    with open(args.db_config, 'r') as f:
        db_config = json.load(f)

    # Connect to PostgreSQL
    with psycopg2.connect(
        host=db_config["host"],
        database=db_config["database"],
        user=db_config["user"],
        password=db_config["password"]
    ) as conn:
        with conn.cursor() as cursor:
            dimension = 768  # Adjust if your embedding model uses a different size
            index = faiss.IndexFlatL2(dimension)

            pdf_dir = Path(args.pdf_dir)
            pdf_files = list(pdf_dir.glob("*.pdf"))
            all_embeddings = []

            for pdf_file in tqdm(pdf_files, desc="Processing PDFs"):
                logging.info(f"Processing {pdf_file}...")

                try:
                    doc_records, embeddings = prepare_for_hybrid_storage(
                        str(pdf_file),
                        max_tokens=args.max_tokens,
                        overlap=args.overlap
                    )
                except Exception as e:
                    logging.error(f"Error processing {pdf_file}: {e}")
                    continue

                if not embeddings:
                    logging.warning(f"No embeddings returned for {pdf_file}. Skipping.")
                    continue

                if len(embeddings[0]) != dimension:
                    logging.error(f"Embedding dimension mismatch: expected {dimension}, got {len(embeddings[0])}")
                    continue

                for record in doc_records:
                    cursor.execute(
                        """
                        INSERT INTO document_chunks 
                        (file_path, file_name, chunk_id, chunk_text, embedding_id)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            record["file_path"],
                            record["file_name"],
                            record["chunk_id"],
                            record["chunk_text"],
                            record["embedding_id"] + len(all_embeddings)
                        )
                    )

                all_embeddings.extend(embeddings)

            conn.commit()

            if not all_embeddings:
                logging.warning("No embeddings were generated. Skipping FAISS index creation.")
                return

            embeddings_array = np.array(all_embeddings).astype('float32')

            if embeddings_array.ndim != 2:
                raise ValueError(f"Expected 2D array for FAISS, got shape {embeddings_array.shape}")

            index.add(embeddings_array)
            faiss.write_index(index, args.faiss_index)

            logging.info(f"Processed {len(all_embeddings)} chunks from {len(pdf_files)} PDFs")
            logging.info(f"FAISS index saved to {args.faiss_index}")

if __name__ == "__main__":
    main()
