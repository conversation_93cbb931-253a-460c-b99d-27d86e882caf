{"appService.zipIgnorePattern": ["__pycache__{,/**}", "*.py[cod]", "*$py.class", ".Python{,/**}", "build{,/**}", "develop-eggs{,/**}", "dist{,/**}", "downloads{,/**}", "eggs{,/**}", ".eggs{,/**}", "lib{,/**}", "lib64{,/**}", "parts{,/**}", "sdist{,/**}", "var{,/**}", "wheels{,/**}", "share/python-wheels{,/**}", "*.egg-info{,/**}", ".installed.cfg", "*.egg", "MANIFEST", ".env{,/**}", ".venv{,/**}", "env{,/**}", "venv{,/**}", "ENV{,/**}", "env.bak{,/**}", "venv.bak{,/**}", ".vscode{,/**}"]}