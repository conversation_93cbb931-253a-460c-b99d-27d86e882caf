import pandas as pd
import psycopg2
import json

# Load DB config
with open('db_config.json', 'r') as f:
    config = json.load(f)

# Connect to DB
conn = psycopg2.connect(
    host=config['host'],
    database=config['name'],
    user=config['user'],
    password=config['password']
)
cur = conn.cursor()

# Create table if it doesn't exist
cur.execute("""
CREATE TABLE IF NOT EXISTS product_sales_comparisons (
    id SERIAL PRIMARY KEY,
    reseller_account TEXT,
    product_category_description TEXT,
    sum_of_invoiced_amount NUMERIC,
    sum_of_forecast_amount_ytd NUMERIC,
    percent_of_goal NUMERIC,
    pyoy_percent_change NUMERIC,
    sum_of_backlog_amount NUMERIC,
    previous_fytd_sum_of_invoiced_amount NUMERIC
)
""")
conn.commit()

# Clear all existing data
cur.execute("DELETE FROM product_sales_comparisons")
conn.commit()

# Load Excel data
file_path = 'datasets/salesforce/Product Sales Comparisons by Rep - NA-2025-06-30-13-12-33.xlsx'
xls = pd.ExcelFile(file_path)
data_start_row = 22
df = pd.read_excel(xls, sheet_name=xls.sheet_names[0], header=data_start_row)

# Set proper column names
df.columns = [
    'blank', 'reseller_account', 'product_category_description',
    'sum_of_invoiced_amount', 'sum_of_forecast_amount_ytd',
    'percent_of_goal', 'pyoy_percent_change', 'sum_of_backlog_amount',
    'previous_fytd_sum_of_invoiced_amount'
]

# Skip the first row, drop unnecessary column
df = df[1:].reset_index(drop=True)
df = df.drop(columns=['blank'])

# Fill down reseller_account values
df['reseller_account'] = df['reseller_account'].ffill()

# Strip and clean string fields
df['reseller_account'] = df['reseller_account'].astype(str).str.strip()
df['product_category_description'] = df['product_category_description'].astype(str).str.strip()

# Remove subtotal/total rows
df = df[~df['reseller_account'].str.lower().isin(['subtotal', 'total'])]

# Drop rows where product_category_description is blank or missing
df = df[df['product_category_description'].str.strip().astype(bool)]

# Coerce numeric fields and fill NaNs with 0.0, round to 2 decimal places
numeric_cols = [
    'sum_of_invoiced_amount',
    'sum_of_forecast_amount_ytd',
    'percent_of_goal',
    'pyoy_percent_change',
    'sum_of_backlog_amount',
    'previous_fytd_sum_of_invoiced_amount'
]
df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce').fillna(0.0).round(2)

# Insert into DB
for _, row in df.iterrows():
    cur.execute("""
        INSERT INTO product_sales_comparisons (
            reseller_account,
            product_category_description,
            sum_of_invoiced_amount,
            sum_of_forecast_amount_ytd,
            percent_of_goal,
            pyoy_percent_change,
            sum_of_backlog_amount,
            previous_fytd_sum_of_invoiced_amount
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        row['reseller_account'],
        row['product_category_description'],
        row['sum_of_invoiced_amount'],
        row['sum_of_forecast_amount_ytd'],
        row['percent_of_goal'],
        row['pyoy_percent_change'],
        row['sum_of_backlog_amount'],
        row['previous_fytd_sum_of_invoiced_amount']
    ))

conn.commit()
cur.close()
conn.close()

print(f"{len(df)} rows inserted successfully.")
