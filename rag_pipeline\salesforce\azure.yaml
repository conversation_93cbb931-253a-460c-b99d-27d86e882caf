# yaml-language-server: $schema=https://raw.githubusercontent.com/Azure/azure-dev/main/schemas/v1.0/azure.yaml.json

# This is an example starter azure.yaml file containing several example services in comments below.
# Make changes as needed to describe your application setup.
# To learn more about the azure.yaml file, visit https://learn.microsoft.com/en-us/azure/developer/azure-developer-cli/azd-schema

# Name of the application.
name: msdocs-python-flask-webapp-quickstart
metadata:
  template: salesforce-query
services:
  web:
    project: .
    language: python
    host: appservice