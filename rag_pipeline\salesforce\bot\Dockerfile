# Use official Python 3.12.3 slim image
FROM python:3.12.3-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Bot Framework configuration for User-Assigned Managed Identity
ENV USE_MANAGED_IDENTITY=true
ENV MicrosoftAppType=UserAssignedMSI

# Set working directory
WORKDIR /app

# Install system dependencies (for building Python packages, if needed)
RUN apt-get update && apt-get install -y gcc

# Copy your app code into the container
COPY . /app

# Install Python dependencies
RUN pip install --upgrade pip \
    && pip install -r requirements.txt

# Expose port for Bot Framework
EXPOSE 3978

# Run the bot
CMD ["python", "app.py"]
