botbuilder/core/__init__.py,sha256=ptFFGVzKyL_SKexxHEsa5gwvD844h3JUoom14lc-pBA,3898
botbuilder/core/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/__pycache__/about.cpython-313.pyc,,
botbuilder/core/__pycache__/activity_handler.cpython-313.pyc,,
botbuilder/core/__pycache__/adapter_extensions.cpython-313.pyc,,
botbuilder/core/__pycache__/auto_save_state_middleware.cpython-313.pyc,,
botbuilder/core/__pycache__/bot.cpython-313.pyc,,
botbuilder/core/__pycache__/bot_adapter.cpython-313.pyc,,
botbuilder/core/__pycache__/bot_assert.cpython-313.pyc,,
botbuilder/core/__pycache__/bot_framework_adapter.cpython-313.pyc,,
botbuilder/core/__pycache__/bot_state.cpython-313.pyc,,
botbuilder/core/__pycache__/bot_state_set.cpython-313.pyc,,
botbuilder/core/__pycache__/bot_telemetry_client.cpython-313.pyc,,
botbuilder/core/__pycache__/card_factory.cpython-313.pyc,,
botbuilder/core/__pycache__/channel_service_handler.cpython-313.pyc,,
botbuilder/core/__pycache__/cloud_adapter_base.cpython-313.pyc,,
botbuilder/core/__pycache__/cloud_channel_service_handler.cpython-313.pyc,,
botbuilder/core/__pycache__/component_registration.cpython-313.pyc,,
botbuilder/core/__pycache__/conversation_reference_extension.cpython-313.pyc,,
botbuilder/core/__pycache__/conversation_state.cpython-313.pyc,,
botbuilder/core/__pycache__/intent_score.cpython-313.pyc,,
botbuilder/core/__pycache__/invoke_response.cpython-313.pyc,,
botbuilder/core/__pycache__/memory_storage.cpython-313.pyc,,
botbuilder/core/__pycache__/memory_transcript_store.cpython-313.pyc,,
botbuilder/core/__pycache__/message_factory.cpython-313.pyc,,
botbuilder/core/__pycache__/middleware_set.cpython-313.pyc,,
botbuilder/core/__pycache__/null_telemetry_client.cpython-313.pyc,,
botbuilder/core/__pycache__/private_conversation_state.cpython-313.pyc,,
botbuilder/core/__pycache__/property_manager.cpython-313.pyc,,
botbuilder/core/__pycache__/queue_storage.cpython-313.pyc,,
botbuilder/core/__pycache__/re_escape.cpython-313.pyc,,
botbuilder/core/__pycache__/recognizer.cpython-313.pyc,,
botbuilder/core/__pycache__/recognizer_result.cpython-313.pyc,,
botbuilder/core/__pycache__/register_class_middleware.cpython-313.pyc,,
botbuilder/core/__pycache__/serializer_helper.cpython-313.pyc,,
botbuilder/core/__pycache__/show_typing_middleware.cpython-313.pyc,,
botbuilder/core/__pycache__/state_property_accessor.cpython-313.pyc,,
botbuilder/core/__pycache__/state_property_info.cpython-313.pyc,,
botbuilder/core/__pycache__/storage.cpython-313.pyc,,
botbuilder/core/__pycache__/telemetry_constants.cpython-313.pyc,,
botbuilder/core/__pycache__/telemetry_logger_constants.cpython-313.pyc,,
botbuilder/core/__pycache__/telemetry_logger_middleware.cpython-313.pyc,,
botbuilder/core/__pycache__/transcript_logger.cpython-313.pyc,,
botbuilder/core/__pycache__/turn_context.cpython-313.pyc,,
botbuilder/core/__pycache__/user_state.cpython-313.pyc,,
botbuilder/core/about.py,sha256=gf-Qf4CuJhq9ZWtKWjfQ-eWL3YN6gqxscmQ4CCD213Y,481
botbuilder/core/activity_handler.py,sha256=3tmdYkcoiIrMl-7mYdn5d_F8tT2QGLYMIU4wuzZwvgY,26425
botbuilder/core/adapter_extensions.py,sha256=l7pnZhSdNbmK3A15ahp-xNdvll-GRmJnmczeEk7bxG8,3327
botbuilder/core/adapters/__init__.py,sha256=FPutsmF1QaMVHIBQ_Zjc4EoiGU0ymXEcRNGgRuIn-8g,414
botbuilder/core/adapters/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/adapters/__pycache__/test_adapter.cpython-313.pyc,,
botbuilder/core/adapters/test_adapter.py,sha256=K9h0xCDezRRXwVzUBC0QQkcnE_6ekXz1_Qunpb7Z0co,25116
botbuilder/core/auto_save_state_middleware.py,sha256=0-DH_pkF6Yds6Ob5agXBOawMLbfCoOhH7pRXCtZzW58,1077
botbuilder/core/bot.py,sha256=WXwKF00aYny-3QEa4dIwEp4gNhBQ9bedSugrEC717ho,538
botbuilder/core/bot_adapter.py,sha256=81zH5qX5GBBDqs3kL52GKjA1JRh2tybF3gsVjlbOFqE,7647
botbuilder/core/bot_assert.py,sha256=OTEE9yrVeiVq5VqzPtzvFGv3gBZKqdxEi02TAMIsetk,2081
botbuilder/core/bot_framework_adapter.py,sha256=XiMmlqgijFGkunQ7mw_TawqaVULKuavsO0Dk0Fvrlh8,56693
botbuilder/core/bot_state.py,sha256=EH0t8AoesnTzq0wSj-1urI47eAe7oAEyYHrYatyIJWY,11649
botbuilder/core/bot_state_set.py,sha256=STGA-cC2sH5CRZPRebPtyFoQm-9TxMiA-OiQ9tBbE90,871
botbuilder/core/bot_telemetry_client.py,sha256=M82HvdfEKQ9SwUZ1quigjCnhh6rEK2OaLSOvrEdsXmo,8763
botbuilder/core/card_factory.py,sha256=9Z0_fecE8C4lQOn8tjSsh5YmZcw39OWkNW52qtUKfWk,6918
botbuilder/core/channel_service_handler.py,sha256=jd4J6Ae6o2iPHBmCOJpgfIU7M7cl6QP_gF3gCddq0kI,18046
botbuilder/core/cloud_adapter_base.py,sha256=GU1ePu3jYoal3UH6T2llf6q47-Mz8OBHZ6wZ8uxIbP8,17285
botbuilder/core/cloud_channel_service_handler.py,sha256=aLkU7iu1UlFUzzOKQDJlvvUnPpr1upGH7g9Y8c_eGmM,653
botbuilder/core/component_registration.py,sha256=Pbet341M-WfciA2WrjkIl9ewzpwcAeufXbXhzh1dnEg,497
botbuilder/core/conversation_reference_extension.py,sha256=4mYttKma0B0JmlssFr3gUKDn2W10nOKHfHF4FzqwJzM,679
botbuilder/core/conversation_state.py,sha256=RJBsS2zAKUVfSkJd-Swxh1KX__x3LEcYdfiNiMOCVtI,2372
botbuilder/core/inspection/__init__.py,sha256=vpqcBc7nZTMDX9mT5jBSIdIstOoYFsviLzLjoipFkHE,484
botbuilder/core/inspection/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/inspection/__pycache__/inspection_middleware.cpython-313.pyc,,
botbuilder/core/inspection/__pycache__/inspection_session.cpython-313.pyc,,
botbuilder/core/inspection/__pycache__/inspection_sessions_by_status.cpython-313.pyc,,
botbuilder/core/inspection/__pycache__/inspection_state.cpython-313.pyc,,
botbuilder/core/inspection/__pycache__/interception_middleware.cpython-313.pyc,,
botbuilder/core/inspection/__pycache__/trace_activity.cpython-313.pyc,,
botbuilder/core/inspection/inspection_middleware.py,sha256=Wutg0uxLSwPCwVLkDnYsuXtDtmglxoCrrgQk0ETyJKQ,7165
botbuilder/core/inspection/inspection_session.py,sha256=PB6xC-UX1E3Fzz5CEMXTZum6gFVnsFqnk17qliDEh0g,1069
botbuilder/core/inspection/inspection_sessions_by_status.py,sha256=ikf6Ml4e-9-HtG6o2fEK4BSMUAychwXzHesSWEFUyjA,654
botbuilder/core/inspection/inspection_state.py,sha256=huriU0EPTxwcPBIHYNi7coO8UxU0RQO3p_VeMd87bbg,444
botbuilder/core/inspection/interception_middleware.py,sha256=zw1JVJ7ie67oLNkv1l3Dqe6VwemPQCEmx0UQ9wPO-3s,3702
botbuilder/core/inspection/trace_activity.py,sha256=hXILPYHrQ9od8dgrw6VXnOOVkktvlvttbVJRkV-3f3U,1890
botbuilder/core/integration/__init__.py,sha256=vOV8f30qk2T4ZHdgD062ZC4Wc91qj9xJbk1W9_RAJD8,563
botbuilder/core/integration/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/integration/__pycache__/aiohttp_channel_service.cpython-313.pyc,,
botbuilder/core/integration/__pycache__/aiohttp_channel_service_exception_middleware.cpython-313.pyc,,
botbuilder/core/integration/aiohttp_channel_service.py,sha256=HCA9EEtxcDr2f6vVws958ak7tgKBQHJ8iy9imoDaR5Q,6634
botbuilder/core/integration/aiohttp_channel_service_exception_middleware.py,sha256=rq5X-ss_1X2Re7Xk3lPSGUU-PSF_B0c_8tEIaDnshPc,891
botbuilder/core/intent_score.py,sha256=ozCRN4W25UPTZmlDpAG3Y6hcfviEytSknODkq8EuXiM,562
botbuilder/core/invoke_response.py,sha256=vG14ZntEC7bzEo7uBGdMcz5dM80fmtF9ytIeZzH5fJY,1459
botbuilder/core/memory_storage.py,sha256=pCYDm2ozSFKkKAn4mKwBD4BAJsgACdvnmGnRapJ0jxE,4372
botbuilder/core/memory_transcript_store.py,sha256=beuZBthzMb63l2LHTOeRNSTOh3CwOp3wgdr0O4KqDg8,5762
botbuilder/core/message_factory.py,sha256=c5ewFcJgw2BQz9KZgif4ZlYSunAXwoqh0o9GGYaHQww,7820
botbuilder/core/middleware_set.py,sha256=EKrIi6NjSWIH3qzSIWdTAv1mqPRJJ6VA00pLdlKQi6I,3054
botbuilder/core/null_telemetry_client.py,sha256=qUbec4V4-RhsqBbKQewX0hfMX9MdHWAt1o_xklgHI70,7842
botbuilder/core/oauth/__init__.py,sha256=DKg0QOIYHarfPSWhTdvt85hzGPlNqexYFF3qJBYbMLk,378
botbuilder/core/oauth/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/oauth/__pycache__/connector_client_builder.cpython-313.pyc,,
botbuilder/core/oauth/__pycache__/extended_user_token_provider.cpython-313.pyc,,
botbuilder/core/oauth/__pycache__/user_token_provider.cpython-313.pyc,,
botbuilder/core/oauth/connector_client_builder.py,sha256=-tQ81i9276MojNlUMivYSnbb5G8X2M5Wpbce0miPyfE,821
botbuilder/core/oauth/extended_user_token_provider.py,sha256=WmCa2U6A-_o7sIRRK0O154o4UTPiBx7AoOohVGdziY0,6480
botbuilder/core/oauth/user_token_provider.py,sha256=8C26wEOeQ5_90TRivKlkf2d0HNRlV_X0MQ79crwp5E0,4521
botbuilder/core/private_conversation_state.py,sha256=7NTVmr2nLXeUy3xseNq1HG3p7rQhUyHN3NvvVnMmfRY,1379
botbuilder/core/property_manager.py,sha256=9OGx6qH4-IlUuqtKuybpznlf8rCTT7Xf9BQnvzgQwZU,296
botbuilder/core/queue_storage.py,sha256=kFAvoXSzTMh3h-j_irQXY7BCkdmzsAjieS3THuRZFws,1181
botbuilder/core/re_escape.py,sha256=dba6b8xg9NIv4aGEfIOBo66b3QbOdOH24vz7bo41mWg,921
botbuilder/core/recognizer.py,sha256=b_73m4mSxx7_KGmWvaoY264zd9pAEPKggAFARNqj1zc,376
botbuilder/core/recognizer_result.py,sha256=wenCnibrDs0bPc7etcMXmJo5LHjTykXifTngVmqd7M0,2077
botbuilder/core/register_class_middleware.py,sha256=ciL8mix7LIqbyntWkPMrmhBeZlzLdTSjE5Mi5Nlftj0,1185
botbuilder/core/serializer_helper.py,sha256=pgN3Ak7n8ubnU-gwqLDEmsbCwAhQBSQJmbvD6NmzgxE,2054
botbuilder/core/show_typing_middleware.py,sha256=xB-QqJzBFk95EE6jlv0Lju_SbpernFjtyKQjWbHNZ8c,3784
botbuilder/core/skills/__init__.py,sha256=KtOuAVk5-OqHy81ojHWhO7hwUMBCVuFDp9kBKxOg_MI,986
botbuilder/core/skills/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/_skill_handler_impl.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/bot_framework_client.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/bot_framework_skill.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/cloud_skill_handler.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/conversation_id_factory.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/skill_conversation_id_factory.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/skill_conversation_id_factory_options.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/skill_conversation_reference.cpython-313.pyc,,
botbuilder/core/skills/__pycache__/skill_handler.cpython-313.pyc,,
botbuilder/core/skills/_skill_handler_impl.py,sha256=98HxbCUwYBIC7vAnaI5jwgoh8M2JgmwHewBd8LGgVnQ,12498
botbuilder/core/skills/bot_framework_client.py,sha256=BLyyVCNIZsaD7lLseuM0Kso52lEhX4ArqESfmoLCxos,484
botbuilder/core/skills/bot_framework_skill.py,sha256=kqfVY8iaIgvSRdiJrxjPnU3Bl6sihZnzj9EjlSQ0W2Y,426
botbuilder/core/skills/cloud_skill_handler.py,sha256=KYdr1wBSzVpMBl8PAN2x0VBzjE8IHgevudbpCebJo2c,4904
botbuilder/core/skills/conversation_id_factory.py,sha256=1x5wlBTJhzCDtUPPV0Q7T2S3yUzh-lEubNN2xFjHnoA,2906
botbuilder/core/skills/skill_conversation_id_factory.py,sha256=KJsUQi_URCGpG_VhY6wVGYApfWfM-EhV81yYMuwGvYY,3471
botbuilder/core/skills/skill_conversation_id_factory_options.py,sha256=OzNUOwR5Dxa46XoejXmXhLyihggqxutlMRO494qyWFg,588
botbuilder/core/skills/skill_conversation_reference.py,sha256=pjWeBYUn7liKi3cI439IqYPNj5IDMui8UAycss7JOLY,463
botbuilder/core/skills/skill_handler.py,sha256=xsCxAUPNT-X1lKLNU6DEFUDBzHGYMmryLM4RmzacNgk,5529
botbuilder/core/state_property_accessor.py,sha256=Sm0MN9N3TslAFDRTJLY5Ti3NVl4x9MkDFfVEJPvgNuo,1151
botbuilder/core/state_property_info.py,sha256=c5-jqht57LLFqlXGr22i8w_0c3sTGbktJ9LfsqGX9Ic,217
botbuilder/core/storage.py,sha256=8QA5vGs0u_UvueiZG_jpBSN5F5sc7knX91KKUCxkEts,1597
botbuilder/core/streaming/__init__.py,sha256=4GoVv7L1pgxBoyee_YRunT2vVhoWhZB8W3NEzOYx2xY,554
botbuilder/core/streaming/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/streaming/__pycache__/bot_framework_http_adapter_base.cpython-313.pyc,,
botbuilder/core/streaming/__pycache__/streaming_activity_processor.cpython-313.pyc,,
botbuilder/core/streaming/__pycache__/streaming_http_client.cpython-313.pyc,,
botbuilder/core/streaming/__pycache__/streaming_request_handler.cpython-313.pyc,,
botbuilder/core/streaming/__pycache__/version_info.cpython-313.pyc,,
botbuilder/core/streaming/bot_framework_http_adapter_base.py,sha256=GzMBjZK-6bgPipTYoLrcqkgAaOWiLTXDLUALISakraE,4821
botbuilder/core/streaming/streaming_activity_processor.py,sha256=OmfN67kxzxxHjq2i-kZQ5ap4QHDyax8oufQEatXeQws,553
botbuilder/core/streaming/streaming_http_client.py,sha256=kCFIG8e2z1XeNVPCSwJtnMnxY8-ZvZt49klItMmIGJc,3270
botbuilder/core/streaming/streaming_request_handler.py,sha256=a8MBnnwmSLboU8_Tf0wCUwrN57VP9QReaDm2BwLwJr4,10294
botbuilder/core/streaming/version_info.py,sha256=SfNwRMVEb934rMtk6eOCxIPSeuPBe9BdDAf-XA1wWAQ,568
botbuilder/core/teams/__init__.py,sha256=OTi_5fLKIlu-aGrCycWNu-g7jyP3idXUaz40-pEpdfs,867
botbuilder/core/teams/__pycache__/__init__.cpython-313.pyc,,
botbuilder/core/teams/__pycache__/teams_activity_extensions.cpython-313.pyc,,
botbuilder/core/teams/__pycache__/teams_activity_handler.cpython-313.pyc,,
botbuilder/core/teams/__pycache__/teams_helper.cpython-313.pyc,,
botbuilder/core/teams/__pycache__/teams_info.cpython-313.pyc,,
botbuilder/core/teams/__pycache__/teams_sso_token_exchange_middleware.cpython-313.pyc,,
botbuilder/core/teams/teams_activity_extensions.py,sha256=PHYN6zvyMQuOJZZM1Ul0_Fiikq9XHK9LlYg72uiMSlQ,2639
botbuilder/core/teams/teams_activity_handler.py,sha256=ZjfH7VOT_Xlr0YWoStIBIzycEBj38og2N8FSPRmAecA,47896
botbuilder/core/teams/teams_helper.py,sha256=1hV_1kyIoLG1CLRW5p3Dg-G6evHjIsrbYtbZX6tEWvM,1187
botbuilder/core/teams/teams_info.py,sha256=3xlsO__zt8vW1CIEFTOECW2w9vyc9nCqwz46Rxuovzc,15122
botbuilder/core/teams/teams_sso_token_exchange_middleware.py,sha256=y0nhkYv2c_l76Ny3kjSV9HQQet_PsoMa7xArlysJNUY,8124
botbuilder/core/telemetry_constants.py,sha256=JjUrl-0Ppd1372DG4VZ9lx3dxUmbJK9dxSFZOeGfR7s,776
botbuilder/core/telemetry_logger_constants.py,sha256=kaRRcZi1pUx-P6-1ebkTi05VU-LpA8MwGsCgqhFK2C4,673
botbuilder/core/telemetry_logger_middleware.py,sha256=mIqZC4ds9totTc5Y31nMJBYqTgyFIk4UsL7SMb8pVYg,13865
botbuilder/core/transcript_logger.py,sha256=xyjmsd6L-14sLl3Hm1mea6MUvdSoMPjDYY2KvFx-8v4,8404
botbuilder/core/turn_context.py,sha256=wk8T4SQtLhZGBdyYb28G8WEIweJBSzWMEJj5k5iFi-0,14813
botbuilder/core/user_state.py,sha256=1rm6ZDxWBFDCdND4mSDvXaf5inrG6bxf1iijGgLf-4s,1452
botbuilder_core-4.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
botbuilder_core-4.17.0.dist-info/METADATA,sha256=0n3J82KismnCViR3Ijw2RBimrkRPju2moFOcpd4AgBA,3912
botbuilder_core-4.17.0.dist-info/RECORD,,
botbuilder_core-4.17.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
botbuilder_core-4.17.0.dist-info/WHEEL,sha256=R0nc6qTxuoLk7ShA2_Y-UWkN8ZdfDBG2B6Eqpz2WXbs,91
botbuilder_core-4.17.0.dist-info/top_level.txt,sha256=2p3tapzF5j-vDQrnQjnuJZvvXStR_qu8i9Tqq-0ThZg,11
