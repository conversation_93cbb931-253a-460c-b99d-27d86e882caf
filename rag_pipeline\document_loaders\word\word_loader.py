from pathlib import Path
from typing import List, Dict
from docx import Document
from langchain_community.embeddings import OllamaEmbeddings
from deepseek_tokenizer import ds_token
import re

def load_docx_text(base_dir: str) -> Dict[str, str]:
    """Recursively searches through directories for .docx files, loads and cleans the text from each file."""
    docx_texts = {}
    base_path = Path(base_dir)
    
    for docx_path in base_path.rglob("*.docx"):
        print(f"Loading Word document: {docx_path}")
        doc = Document(docx_path)
        text_parts = []

        for i, para in enumerate(doc.paragraphs):
            para_text = para.text
            text_parts.append(para_text)
            print(f"Paragraph {i+1}: Extracted {len(para_text)} characters")

        full_text = "\n".join(text_parts)
        cleaned_text = re.sub(r'\n\s*\n+', '\n\n', full_text)
        docx_texts[str(docx_path)] = cleaned_text
    
    return docx_texts

def chunk_text(text: str, max_tokens: int = 512, overlap: int = 64) -> List[str]:
    """Chunks text into overlapping segments using Deepseek tokenizer."""
    print(f"Chunking text with max_tokens={max_tokens}, overlap={overlap}")
    tokens = ds_token.encode(text)
    print(f"Text encoded to {len(tokens)} tokens")
    chunks = []
    start = 0

    while start < len(tokens):
        end = min(start + max_tokens, len(tokens))
        chunk = ds_token.decode(tokens[start:end])
        chunks.append(chunk)
        print(f"Created chunk {len(chunks)}: {start}-{end} ({end-start} tokens, {len(chunk)} chars)")
        start += max_tokens - overlap

    print(f"Created {len(chunks)} chunks in total")
    return chunks

def prepare_for_hybrid_storage(base_dir: str, max_tokens: int = 512, overlap: int = 64):
    """Prepares Word document content for hybrid storage (PostgreSQL + FAISS)."""
    print(f"\n{'='*50}\nProcessing documents in {base_dir} for hybrid storage\n{'='*50}")

    print("Initializing Ollama embeddings model")
    embedding_model = OllamaEmbeddings(
        model="nomic-embed-text:latest",
        base_url="http://localhost:11434"
    )

    docx_texts = load_docx_text(base_dir)
    all_document_records = []
    all_embeddings = []

    for docx_path, text in docx_texts.items():
        chunks = chunk_text(text, max_tokens, overlap)
        file_name = Path(docx_path).name
        print(f"File name: {file_name}")

        print(f"Generating embeddings for {len(chunks)} chunks...")
        try:
            embeddings = embedding_model.embed_documents(chunks)
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            continue

        print(f"Generated {len(embeddings)} embeddings")
        if embeddings:
            print(f"Embedding dimensions: {len(embeddings[0])}")

        document_records = []
        for i, chunk in enumerate(chunks):
            document_records.append({
                "file_path": str(docx_path),
                "file_name": file_name,
                "chunk_id": i,
                "chunk_text": chunk,
                "embedding_id": i,
                "page_number": -1  # Word docs don't have explicit pages. Pagination is handled by the Word rendering engine, which applies margins, font sizes, line spacing, and printer settings to compute where page breaks occur.
            })
            if i < 2 or i == len(chunks) - 1:
                print(f"Record {i}: {document_records[i]}")
            elif i == 2:
                print("...")

        print(f"Created {len(document_records)} document records")
        all_document_records.extend(document_records)
        all_embeddings.extend(embeddings)

    print(f"Processing complete for documents in {base_dir}\n{'='*50}")
    return all_document_records, all_embeddings

    """Prepares Word document content for hybrid storage (PostgreSQL + FAISS)."""
    print(f"\n{'='*50}\nProcessing documents in {base_dir} for hybrid storage\n{'='*50}")

    print("Initializing Ollama embeddings model")
    embedding_model = OllamaEmbeddings(
        model="nomic-embed-text:latest",
        base_url="http://localhost:11434"  # Update this to your actual Ollama endpoint
    )

    docx_texts = load_docx_text(base_dir)
    all_document_records = []
    all_embeddings = []

    for docx_path, text in docx_texts.items():
        chunks = chunk_text(text, max_tokens, overlap)
        file_name = Path(docx_path).name
        print(f"File name: {file_name}")

        print(f"Generating embeddings for {len(chunks)} chunks...")
        try:
            embeddings = embedding_model.embed_documents(chunks)
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            continue

        print(f"Generated {len(embeddings)} embeddings")
        if embeddings:
            print(f"Embedding dimensions: {len(embeddings[0])}")

        document_records = []
        for i, chunk in enumerate(chunks):
            document_records.append({
                "file_path": docx_path,
                "file_name": file_name,
                "chunk_id": i,
                "chunk_text": chunk,
                "embedding_id": i
            })
            if i < 2 or i == len(chunks) - 1:
                print(f"Record {i}: {document_records[i]}")
            elif i == 2:
                print("...")

        print(f"Created {len(document_records)} document records")
        all_document_records.extend(document_records)
        all_embeddings.extend(embeddings)

    print(f"Processing complete for documents in {base_dir}\n{'='*50}")
    return all_document_records, all_embeddings
