import os
import argparse
from pathlib import Path
import json
import numpy as np
import logging
from tqdm import tqdm
import psycopg2
import faiss

from document_loaders.pdf.pdf_loader import (
    prepare_for_hybrid_storage as prepare_pdf,
    load_all_pdf_paths
)

from document_loaders.word.word_loader import (
    load_docx_text,
    chunk_text as chunk_docx_text
)

from langchain_community.embeddings import OllamaEmbeddings

def process_word_docs(base_dir: str, max_tokens: int, overlap: int, dimension: int, cursor, all_embeddings):
    logging.info(f"Searching for Word documents in: {base_dir}")
    embedding_model = OllamaEmbeddings(
        model="nomic-embed-text:latest",
        base_url="http://localhost:11434"
    )

    docx_texts = load_docx_text(base_dir)

    for docx_path, text in tqdm(docx_texts.items(), desc="Processing Word docs"):
        file_name = Path(docx_path).name
        chunks = chunk_docx_text(text, max_tokens, overlap)

        if not chunks:
            logging.warning(f"No chunks generated from {docx_path}")
            continue

        try:
            embeddings = embedding_model.embed_documents(chunks)
        except Exception as e:
            logging.error(f"Error generating embeddings for {docx_path}: {e}")
            continue

        if len(embeddings[0]) != dimension:
            logging.error(f"Embedding dimension mismatch in {docx_path}: expected {dimension}, got {len(embeddings[0])}")
            continue

        for i, chunk in enumerate(chunks):
            cursor.execute(
                """
                INSERT INTO document_chunks 
                (file_path, file_name, chunk_id, chunk_text, embedding_id, page_number)
                VALUES (%s, %s, %s, %s, %s, %s)
                """,
                (
                    str(docx_path),
                    file_name,
                    i,
                    chunk,
                    i + len(all_embeddings),
                    -1  # Word docs don't expose page numbers
                )
    )


        all_embeddings.extend(embeddings)


def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser(description="Process PDFs and Word docs for RAG pipeline")
    parser.add_argument("--input_dir", type=str, required=True, help="Directory containing documents")
    parser.add_argument("--db_config", type=str, required=True, help="Path to database config JSON file")
    parser.add_argument("--faiss_index", type=str, required=True, help="Path to save FAISS index")
    parser.add_argument("--max_tokens", type=int, default=512, help="Maximum tokens per chunk")
    parser.add_argument("--overlap", type=int, default=64, help="Overlap tokens between chunks")
    args = parser.parse_args()

    # Load DB config
    with open(args.db_config, 'r') as f:
        db_config = json.load(f)

    # Connect to PostgreSQL
    with psycopg2.connect(
        host=db_config["host"],
        database=db_config["database"],
        user=db_config["user"],
        password=db_config["password"]
    ) as conn:
        with conn.cursor() as cursor:
            dimension = 768
            index = faiss.IndexFlatL2(dimension)
            all_embeddings = []

            # Process PDFs
            pdf_files = load_all_pdf_paths(args.input_dir)
            for pdf_file in tqdm(pdf_files, desc="Processing PDFs"):
                logging.info(f"Processing {pdf_file}...")
                try:
                    doc_records, embeddings = prepare_pdf(
                        str(pdf_file),
                        max_tokens=args.max_tokens,
                        overlap=args.overlap
                    )
                except Exception as e:
                    logging.error(f"Error processing {pdf_file}: {e}")
                    continue

                if not embeddings:
                    logging.warning(f"No embeddings returned from {pdf_file}")
                    continue

                if len(embeddings[0]) != dimension:
                    logging.error(f"Embedding dimension mismatch in {pdf_file}")
                    continue

                for record in doc_records:
                    cursor.execute(
                        """
                        INSERT INTO document_chunks 
                        (file_path, file_name, chunk_id, chunk_text, embedding_id, page_number)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """,
                        (
                            record["file_path"],
                            record["file_name"],
                            record["chunk_id"],
                            record["chunk_text"],
                            record["embedding_id"] + len(all_embeddings),
                            record.get("page_number", -1)
                        )
                    )

                all_embeddings.extend(embeddings)

            # Process Word documents
            process_word_docs(
                base_dir=args.input_dir,
                max_tokens=args.max_tokens,
                overlap=args.overlap,
                dimension=dimension,
                cursor=cursor,
                all_embeddings=all_embeddings
            )

            conn.commit()

            if len(all_embeddings) == 0:
                logging.error("No embeddings were collected. Exiting without creating FAISS index.")
                return

            embeddings_array = np.vstack(all_embeddings).astype('float32')
            index.add(embeddings_array)
            faiss.write_index(index, args.faiss_index)

            logging.info(f"Processed {len(all_embeddings)} chunks from {len(pdf_files)} PDFs and Word docs combined")
            logging.info(f"FAISS index saved to {args.faiss_index}")

if __name__ == "__main__":
    main()
