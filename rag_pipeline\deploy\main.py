from fastapi import FastAPI
from pydantic import BaseModel
from openai import AzureOpenAI
from dotenv import load_dotenv
from langchain_community.utilities import SQLDatabase
from sqlalchemy import create_engine, text
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from urllib.parse import quote_plus
import psycopg2
import os
import json
import re

# Load env vars
load_dotenv()

app = FastAPI()

# Input model for POST /query
class QueryRequest(BaseModel):
    question: str

# Get DB and OpenAI credentials
def get_secrets():
    is_local = os.getenv("ENV", "").lower() == "local"
    print(f"Running locally: {is_local}")
    if is_local:
        with open("db_config.json", "r") as f:
            db_config = json.load(f)

        return {
            "db_user": db_config["user"],
            "db_pass": db_config["password"],
            "db_host": db_config["host"],
            "db_name": db_config["name"],
            "openai_api_key": os.getenv("AZURE_OPENAI_API_KEY"),
            "openai_endpoint": os.getenv("AZURE_OPENAI_ENDPOINT"),
            "deployment_name": os.getenv("AZURE_OPENAI_DEPLOYMENT")
        }

    else:
        key_vault_url = "https://ArmstrongAIKeyVault.vault.azure.net"
        credential = DefaultAzureCredential()
        client = SecretClient(vault_url=key_vault_url, credential=credential)

        return {
            "db_user": client.get_secret("db-user").value,
            "db_pass": client.get_secret("db-password").value,
            "db_host": client.get_secret("db-host").value,
            "db_name": client.get_secret("db-name").value,
            "openai_api_key": client.get_secret("openai-api-key").value,
            "openai_endpoint": client.get_secret("openai-endpoint").value,
            "deployment_name": client.get_secret("openai-deployment").value
        }

# Create SQLAlchemy engine
def get_engine(secrets):
    escaped_password = quote_plus(secrets["db_pass"])
    conn_str = f"postgresql+psycopg2://{secrets['db_user']}:{escaped_password}@{secrets['db_host']}/{secrets['db_name']}"
    return create_engine(conn_str)

# Endpoint: POST /query
@app.post("/query")
def run_query(request: QueryRequest):
    question = request.question

    try:
        secrets = get_secrets()
        engine = get_engine(secrets)

        # Get DB schema for prompt
        db = SQLDatabase(engine)
        schema_description = db.get_table_info(["product_sales_comparisons"])

        # Construct schema-aware prompt
        prompt = f"""
        You are a helpful assistant that translates natural language questions into SQL queries.

        Here is the database schema:
        {schema_description}

        Column mappings:
        - reseller_account → Reseller Account, rep, representative, sales rep
        - product_category_description → product, product category, category, line of business

        Language Guidance:
        - "absolute" or "overall" means aggregate across all `product_category_description` (i.e., sum across categories)
        - When a product name is mentioned (e.g., "Hot Water", "Steam"), filter using `product_category_description`

        Translate the following question into a valid SQL query:
        \"\"\"{question}\"\"\"
        Only return the SQL statement, nothing else.
        """

        # Call Azure OpenAI
        openai_client = AzureOpenAI(
            api_key=secrets["openai_api_key"],
            azure_endpoint=secrets["openai_endpoint"],
            api_version="2024-02-15-preview"
        )

        response = openai_client.chat.completions.create(
            model=secrets["deployment_name"],
            messages=[
                {"role": "system", "content": "You are a SQL expert."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            max_tokens=500
        )

        raw_sql = response.choices[0].message.content
        generated_sql = re.sub(r"```(?:sql)?|```", "", raw_sql).strip()

        # Run the generated SQL
        conn = psycopg2.connect(
            host=secrets["db_host"],
            database=secrets["db_name"],
            user=secrets["db_user"],
            password=secrets["db_pass"]
        )
        cur = conn.cursor()
        cur.execute(generated_sql)
        rows = cur.fetchall()
        col_names = [desc[0] for desc in cur.description]
        results = [dict(zip(col_names, row)) for row in rows]
        cur.close()
        conn.close()

        return {
            "question": question,
            "generated_sql": generated_sql,
            "results": results
        }

    except Exception as e:
        return {"error": str(e)}
