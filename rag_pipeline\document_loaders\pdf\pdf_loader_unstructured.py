from pathlib import Path
from typing import List, Dict, Any
from unstructured.partition.pdf import partition_pdf
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.embeddings import OllamaEmbeddings


def load_pdf_with_unstructured(pdf_path: str) -> List[Dict[str, Any]]:
    """Parses a PDF using Unstructured and returns structured elements with metadata."""
    print(f"Parsing PDF with Unstructured: {pdf_path}")
    elements = partition_pdf(filename=pdf_path, strategy="hi_res")  # Use "fast" for speed
    structured_chunks = []

    for el in elements:
        if el.text:
            structured_chunks.append({
                "text": el.text.strip(),
                "page_number": el.metadata.page_number,
                "category": el.category,
                "section_title": getattr(el.metadata, "section_title", None)
            })

    print(f"Extracted {len(structured_chunks)} structured elements")
    return structured_chunks


def chunk_and_embed(structured_chunks: List[Dict[str, Any]], file_path: str, max_tokens: int = 512, overlap: int = 64):
    """Chunks structured text and generates embeddings with metadata."""
    print("Initializing embedding model...")
    embedding_model = OllamaEmbeddings(
        model="nomic-embed-text:latest",
        base_url="http://localhost:11434"  # Update if needed
    )

    splitter = RecursiveCharacterTextSplitter(chunk_size=max_tokens, chunk_overlap=overlap)
    all_chunks = []
    chunk_id = 0

    for entry in structured_chunks:
        split_docs = splitter.split_text(entry["text"])
        for chunk in split_docs:
            all_chunks.append({
                "chunk_id": chunk_id,
                "chunk_text": chunk,
                "file_path": file_path,
                "file_name": Path(file_path).name,
                "page_number": entry.get("page_number"),
                "section_title": entry.get("section_title"),
                "category": entry.get("category"),
            })
            chunk_id += 1

    print(f"Total chunks created: {len(all_chunks)}")
    print("Generating embeddings...")
    texts = [c["chunk_text"] for c in all_chunks]
    embeddings = embedding_model.embed_documents(texts)

    for i, chunk in enumerate(all_chunks):
        chunk["embedding_id"] = i
        if i < 2 or i == len(all_chunks) - 1:
            print(f"Chunk {i}: {chunk['chunk_text'][:100]}...")

    return all_chunks, embeddings


def prepare_for_hybrid_storage(pdf_path: str, max_tokens: int = 512, overlap: int = 64):
    """Prepares PDF content for hybrid storage (PostgreSQL + FAISS)."""
    print(f"\n{'='*50}\nProcessing {pdf_path} for hybrid storage\n{'='*50}")
    try:
        structured_chunks = load_pdf_with_unstructured(pdf_path)
        if not structured_chunks:
            print("No text extracted from PDF.")
            return [], []
        return chunk_and_embed(structured_chunks, pdf_path, max_tokens, overlap)
    except Exception as e:
        print(f"Error processing PDF: {e}")
        return [], []
