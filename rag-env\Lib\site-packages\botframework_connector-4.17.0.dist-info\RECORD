botframework/connector/__init__.py,sha256=wjPlOZlESfb8CuPl6A1S8_jFvrl9FJ-djdu_mlnk3sU,1053
botframework/connector/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/__pycache__/_configuration.cpython-313.pyc,,
botframework/connector/__pycache__/_not_implemented_http_client.cpython-313.pyc,,
botframework/connector/__pycache__/about.cpython-313.pyc,,
botframework/connector/__pycache__/aiohttp_bf_pipeline.cpython-313.pyc,,
botframework/connector/__pycache__/bot_framework_sdk_client_async.cpython-313.pyc,,
botframework/connector/__pycache__/channels.cpython-313.pyc,,
botframework/connector/__pycache__/connector_client.cpython-313.pyc,,
botframework/connector/__pycache__/emulator_api_client.cpython-313.pyc,,
botframework/connector/__pycache__/http_client_base.cpython-313.pyc,,
botframework/connector/__pycache__/http_client_factory.cpython-313.pyc,,
botframework/connector/__pycache__/http_request.cpython-313.pyc,,
botframework/connector/__pycache__/http_response_base.cpython-313.pyc,,
botframework/connector/__pycache__/version.cpython-313.pyc,,
botframework/connector/_configuration.py,sha256=CXOJq_0cax9FhG_2Jj5sw-zIV0s6cAwgZ2qiDDm7iL4,1292
botframework/connector/_not_implemented_http_client.py,sha256=Uur5NcYQVes0G236xJtPk8ek7IEkriK2cYQPBhoFxks,521
botframework/connector/about.py,sha256=xD5TinkB1DykdeWZh6-GGJSyyrsHdGr6eI1aMLG8X3Y,474
botframework/connector/aio/__init__.py,sha256=ySrUHJBUujXAn5mZ1ZfGknF8H8DeoU6ho1cecV4hKp8,411
botframework/connector/aio/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/aio/__pycache__/_connector_client_async.cpython-313.pyc,,
botframework/connector/aio/_connector_client_async.py,sha256=rRPInvRJxupyb1ZoYfhqcQptIXSuORP-rC8ZDVCWK5s,3877
botframework/connector/aio/operations_async/__init__.py,sha256=FIaZUTUJcTdEDj7x1PU_JL2ZfpXhwBlbxmAeNLwl7oo,525
botframework/connector/aio/operations_async/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/aio/operations_async/__pycache__/_attachments_operations_async.cpython-313.pyc,,
botframework/connector/aio/operations_async/__pycache__/_conversations_operations_async.cpython-313.pyc,,
botframework/connector/aio/operations_async/_attachments_operations_async.py,sha256=BgN2jcv0Uob_xCoutDz-o6lQeqd1uEN0jk-fFmwIMTs,5938
botframework/connector/aio/operations_async/_conversations_operations_async.py,sha256=-I382JgsxCYkgtPG8V1OpONpeuTvBiyqyo_MlASPGPk,44184
botframework/connector/aiohttp_bf_pipeline.py,sha256=bzGrm0Uu2ZgNZMLTg1cByBoP8XlTXdzHry5KHdDvwcY,1402
botframework/connector/async_mixin/__init__.py,sha256=e1k2cKcFhUh08C9rYkbiS6mZJw3c_nyGXwpV14Vr99s,144
botframework/connector/async_mixin/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/async_mixin/__pycache__/async_mixin.cpython-313.pyc,,
botframework/connector/async_mixin/async_mixin.py,sha256=n_VOGOpAFzCAkyL9CxO2nOqOl7bwEoBqYv9RcReUuYk,6953
botframework/connector/auth/__init__.py,sha256=IMo6O6h1UKTlWbY_plPEumfvD70pJok-_Gm61k5GyJs,1499
botframework/connector/auth/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/auth/__pycache__/_bot_framework_client_impl.cpython-313.pyc,,
botframework/connector/auth/__pycache__/_built_in_bot_framework_authentication.cpython-313.pyc,,
botframework/connector/auth/__pycache__/_connector_factory_impl.cpython-313.pyc,,
botframework/connector/auth/__pycache__/_government_cloud_bot_framework_authentication.cpython-313.pyc,,
botframework/connector/auth/__pycache__/_parameterized_bot_framework_authentication.cpython-313.pyc,,
botframework/connector/auth/__pycache__/_public_cloud_bot_framework_authentication.cpython-313.pyc,,
botframework/connector/auth/__pycache__/_user_token_client_impl.cpython-313.pyc,,
botframework/connector/auth/__pycache__/app_credentials.cpython-313.pyc,,
botframework/connector/auth/__pycache__/authenticate_request_result.cpython-313.pyc,,
botframework/connector/auth/__pycache__/authentication_configuration.cpython-313.pyc,,
botframework/connector/auth/__pycache__/authentication_constants.cpython-313.pyc,,
botframework/connector/auth/__pycache__/bot_framework_authentication.cpython-313.pyc,,
botframework/connector/auth/__pycache__/bot_framework_authentication_factory.cpython-313.pyc,,
botframework/connector/auth/__pycache__/certificate_app_credentials.cpython-313.pyc,,
botframework/connector/auth/__pycache__/certificate_government_app_credentials.cpython-313.pyc,,
botframework/connector/auth/__pycache__/certificate_service_client_credential_factory.cpython-313.pyc,,
botframework/connector/auth/__pycache__/channel_provider.cpython-313.pyc,,
botframework/connector/auth/__pycache__/channel_validation.cpython-313.pyc,,
botframework/connector/auth/__pycache__/claims_identity.cpython-313.pyc,,
botframework/connector/auth/__pycache__/connector_factory.cpython-313.pyc,,
botframework/connector/auth/__pycache__/credential_provider.cpython-313.pyc,,
botframework/connector/auth/__pycache__/emulator_validation.cpython-313.pyc,,
botframework/connector/auth/__pycache__/endorsements_validator.cpython-313.pyc,,
botframework/connector/auth/__pycache__/enterprise_channel_validation.cpython-313.pyc,,
botframework/connector/auth/__pycache__/government_channel_validation.cpython-313.pyc,,
botframework/connector/auth/__pycache__/government_constants.cpython-313.pyc,,
botframework/connector/auth/__pycache__/jwt_token_extractor.cpython-313.pyc,,
botframework/connector/auth/__pycache__/jwt_token_validation.cpython-313.pyc,,
botframework/connector/auth/__pycache__/managedidentity_app_credentials.cpython-313.pyc,,
botframework/connector/auth/__pycache__/managedidentity_service_client_credential_factory.cpython-313.pyc,,
botframework/connector/auth/__pycache__/microsoft_app_credentials.cpython-313.pyc,,
botframework/connector/auth/__pycache__/microsoft_government_app_credentials.cpython-313.pyc,,
botframework/connector/auth/__pycache__/password_service_client_credential_factory.cpython-313.pyc,,
botframework/connector/auth/__pycache__/service_client_credentials_factory.cpython-313.pyc,,
botframework/connector/auth/__pycache__/simple_channel_provider.cpython-313.pyc,,
botframework/connector/auth/__pycache__/skill_validation.cpython-313.pyc,,
botframework/connector/auth/__pycache__/user_token_client.cpython-313.pyc,,
botframework/connector/auth/__pycache__/verify_options.cpython-313.pyc,,
botframework/connector/auth/_bot_framework_client_impl.py,sha256=UueljzIbJBL6jv_uuAS0kjgd7f0r4WuzIFKC1FOTdkY,4473
botframework/connector/auth/_built_in_bot_framework_authentication.py,sha256=gER08q63Iv9eTyo2Iu57GZkYTjZg9KkSC_QF9UH92GM,8892
botframework/connector/auth/_connector_factory_impl.py,sha256=TkbzHqO5XHJHfwVQNw_sQ0bukbFMsLtNBsZdzUO5TTc,2271
botframework/connector/auth/_government_cloud_bot_framework_authentication.py,sha256=mg61jkQXOqD9Oxm9y_CVl3YJEKqHuCQFgzFDWAMIYeU,1526
botframework/connector/auth/_parameterized_bot_framework_authentication.py,sha256=vJgG9kWhtyKW-Pz3x4dtROui2BhPt7gVy9XIHXg3PSc,21920
botframework/connector/auth/_public_cloud_bot_framework_authentication.py,sha256=uCkPDlb7arMy9cXqYmHEVLGSJrW7432f3_O1xSX-OPk,1527
botframework/connector/auth/_user_token_client_impl.py,sha256=OUyk8kTCSXsbeYj4j8F4wSBKjo2YIFWgEub9TJaK-TI,4856
botframework/connector/auth/app_credentials.py,sha256=NnY4BiApdDmB-Ho__Bs47pmzolZ5puHZ-fzg0Lwo-64,4050
botframework/connector/auth/authenticate_request_result.py,sha256=sCmEWI1RG8_XRInjCXgltHFmKcqEHXoVEL_xpE9OLlI,590
botframework/connector/auth/authentication_configuration.py,sha256=994uWqf80CmKaVhCfF3Dlk4gEN-ypzDExjJhmzBsynk,1562
botframework/connector/auth/authentication_constants.py,sha256=xA-YKsP_gQ_MFru1mVi62az8rxkgXK93QFKe0MlQM9w,6021
botframework/connector/auth/bot_framework_authentication.py,sha256=fIWRtHaYrY-33vm_XoT99LSVCWf5NBftDcsGV47VhQo,4151
botframework/connector/auth/bot_framework_authentication_factory.py,sha256=IqgoIkwkkuODUw7pZ91-CedyiWvBeeEjxJy-4_hE_9s,5259
botframework/connector/auth/certificate_app_credentials.py,sha256=xdLJqkzeWOY7xE8y2Fas9Mqktebp4xBf4E_U3x6d-WU,3581
botframework/connector/auth/certificate_government_app_credentials.py,sha256=NiFJe5hFv-glQHHOaCJGMQGYvVGzstVfEytbn7YKaFg,1849
botframework/connector/auth/certificate_service_client_credential_factory.py,sha256=01Y58RZDVB0ODo4vfmva3w4mBBtQbHk4e9HmrAL4yLo,4323
botframework/connector/auth/channel_provider.py,sha256=WiFveH_Z7xTBpiLizH9G7Z-BbV7o4DNtTMDBvg1fRuQ,655
botframework/connector/auth/channel_validation.py,sha256=H9wYi3Q0ASOMjazh-U-NYGCzxTnSEzkaHdoa9uia-SY,5907
botframework/connector/auth/claims_identity.py,sha256=8Biia4t_o97VHqfVJFnjDXQe6gYTiVwy-Q9iX8C6Rl8,573
botframework/connector/auth/connector_factory.py,sha256=DRQshozNEwPJMDLkNctf8Kr9r-iZC6VJ2MRnKmNUqck,671
botframework/connector/auth/credential_provider.py,sha256=ZbSEVp5nB1l8MGn2LtuYH_T4cEU0hGVCoNLG4T-Sb5U,2686
botframework/connector/auth/emulator_validation.py,sha256=sNn0XNFz2yN4Jo6nPTKn2sHQvqnIazdC2gOrNatElB0,7688
botframework/connector/auth/endorsements_validator.py,sha256=wqUFzaWIh8lWQ4YdT3k7yqBbB76aHFTak7STigzH9qM,1282
botframework/connector/auth/enterprise_channel_validation.py,sha256=A7o23ZLLF8yplFFQYHNGukz2BnukuvzFPV7c30_2nec,4998
botframework/connector/auth/government_channel_validation.py,sha256=j8e_FZ5Z7E0xtN7gCavauyK_yDUocjAFa_XrT7i8gMs,4515
botframework/connector/auth/government_constants.py,sha256=T-7t1PN6D4h2fPP3XyesIDo6I1SroXcbNv-_pKRbAx0,1582
botframework/connector/auth/jwt_token_extractor.py,sha256=TfodgWhKe6_2nTbup2ZIishwYr5uHo8v6WL1LXqNxgc,5627
botframework/connector/auth/jwt_token_validation.py,sha256=Uez1tf6MKUL-PzJPt4K8bUCRv6OE0NiYSEee1iMeRMk,9250
botframework/connector/auth/managedidentity_app_credentials.py,sha256=N3xK5eJhMxbXSU6j7poxTEoshq8xRc7Aprr9rlsd8zk,1717
botframework/connector/auth/managedidentity_service_client_credential_factory.py,sha256=T_EIoAB59q5Hxb9abiPRqyWvwuUQKL7N1KvqQdksL3Q,1288
botframework/connector/auth/microsoft_app_credentials.py,sha256=MfNEXYXvR_fS_CNOhug0sA-Dq5oJZrldzJyipn4jss8,2475
botframework/connector/auth/microsoft_government_app_credentials.py,sha256=kiC_K4-f_v_rJcDSBoSy0y_SdkCCbn5Q6wD7FQHbqkU,1156
botframework/connector/auth/password_service_client_credential_factory.py,sha256=o1auRdWdhfWOkXmMTO5lebpycEvcnSTdwtFZwTaMSfk,3034
botframework/connector/auth/service_client_credentials_factory.py,sha256=bSj6AKPJstWbonPdmowreIL3hfsFdSfigUvdrBBZ-1w,1371
botframework/connector/auth/simple_channel_provider.py,sha256=s_X62J8nW8H5gbLtMF9Ro8UtaL1LsDn5kMaE2OH8ueo,821
botframework/connector/auth/skill_validation.py,sha256=_R4RHw_TDlipT1vXHWH7ssQFbF75aFDOC9C4Aodw2go,7277
botframework/connector/auth/user_token_client.py,sha256=NpQI3ANAOanvcPvdZbXK3Cyw08vxziZew7X9oSoBMyI,5528
botframework/connector/auth/verify_options.py,sha256=gjtzgtO8gtK-lgPDNNTcRrsa-uQGYjRG9IJfQzv77VE,495
botframework/connector/bot_framework_sdk_client_async.py,sha256=sxSvSWbUGXuGOLoprWieLit4TazW0RFZhJKKm8vyg84,1263
botframework/connector/channels.py,sha256=FSp0BiW3Lsmcy-6CJUAzevM5QXPgGddB9e2xE4LD8Ao,1182
botframework/connector/connector_client.py,sha256=Us4KCYTmK54xH9Lcelzx4FIIMHKeeVqWNxaR6-WJKh8,3074
botframework/connector/emulator_api_client.py,sha256=0ICJQYD6KenzJCcTVpgTNY82A78SSjjo5O5_F6eX7Ag,815
botframework/connector/http_client_base.py,sha256=0ZUrLphIY_Q859v5myg6WWId6sBat0jMkFZA9tKFPa8,375
botframework/connector/http_client_factory.py,sha256=dUTDssCHzZ4KhIH5SD46i4iZpN-6HnVAT2JW_787WUg,227
botframework/connector/http_request.py,sha256=eTbWuzx2btrOdtWUwp_YRdUGcAxIfG3yLMbL4_mUMqo,405
botframework/connector/http_response_base.py,sha256=iRTVm9Ej3pxFlPLprg-WGcXBXQvA-mptyArrd3-baAM,539
botframework/connector/models/__init__.py,sha256=ZAsxpMr7lehR5OfgEbV1sjd4rcvdyhUoHmVfSE-ku4I,397
botframework/connector/models/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/operations/__init__.py,sha256=h0a4VXA1z8Uvtq9GOnZVXz-r_uIP0oBc1tHh_0LzyPk,513
botframework/connector/operations/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/operations/__pycache__/_attachments_operations.cpython-313.pyc,,
botframework/connector/operations/__pycache__/_conversations_operations.cpython-313.pyc,,
botframework/connector/operations/_attachments_operations.py,sha256=1ovRV6Oy4vcwVIjXBdKzrfvPx498TatBprz6tntsCqM,5823
botframework/connector/operations/_conversations_operations.py,sha256=3cYfXVh6UlgFWvRUgZEGUQ44YEmqrL5axTRxb-12ldE,43593
botframework/connector/skills/__init__.py,sha256=AEAaP_UChEDX1CgP0VQXqVUGdcWADjuRMDyUvuBY5Ok,87
botframework/connector/skills/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/skills/__pycache__/bot_framework_client.cpython-313.pyc,,
botframework/connector/skills/bot_framework_client.py,sha256=tf6Ocad1ZALoIshlTHj8ALMu6ZUyEXPa-qrIqcS03kY,1076
botframework/connector/teams/__init__.py,sha256=bvVmO1Arc4QDbFiVNAzXTtO-M7DCLlY1w4g3JoaWvgg,472
botframework/connector/teams/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/teams/__pycache__/teams_connector_client.cpython-313.pyc,,
botframework/connector/teams/__pycache__/version.cpython-313.pyc,,
botframework/connector/teams/operations/__init__.py,sha256=z97XCCaQuYdHzCzDv-2oI8u__ajUFOT1kVWioJgDx0U,411
botframework/connector/teams/operations/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/teams/operations/__pycache__/teams_operations.cpython-313.pyc,,
botframework/connector/teams/operations/teams_operations.py,sha256=tq0pm-FwyAg8tPuerzBQPxfq0HvwX2P6vm39IXGhVLU,12462
botframework/connector/teams/teams_connector_client.py,sha256=IxnpULJwSnGgmIcLwO5mp9to5SZlonAxCR_5rV2D7ro,3255
botframework/connector/teams/version.py,sha256=2fJfNcCtZEx_7O6-SKq6X4F_a2FDD_ZhOXX8gJ_Fb5w,342
botframework/connector/token_api/__init__.py,sha256=Xn6mwe4hi93jgGDndVLiLH326Czj22j165ULYm3IaaM,543
botframework/connector/token_api/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/token_api/__pycache__/_configuration.cpython-313.pyc,,
botframework/connector/token_api/__pycache__/_token_api_client.cpython-313.pyc,,
botframework/connector/token_api/__pycache__/version.cpython-313.pyc,,
botframework/connector/token_api/_configuration.py,sha256=5N-iFbamZSp6rKVAVb_1rV_I2q76ODPvpqom-ScHLTo,1287
botframework/connector/token_api/_token_api_client.py,sha256=XEaVMSoaGqzOjQrKsNQRAgFMjlWgnoCcTIU1eCTj4Pc,1926
botframework/connector/token_api/aio/__init__.py,sha256=3wntjCCM9uS1Ey1m7GOPiu0nKa-FGVSELQFol3nm7hI,409
botframework/connector/token_api/aio/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/token_api/aio/__pycache__/_token_api_client_async.cpython-313.pyc,,
botframework/connector/token_api/aio/_token_api_client_async.py,sha256=LiVMZHJUEK8UTc3vJbRKSM2GyWa4h0nHSLgmgpkGha0,1955
botframework/connector/token_api/aio/operations_async/__init__.py,sha256=djjh1RcRvEAlGxHDLXhdpFyEiIwNOliZqx5s_zQV9Nc,510
botframework/connector/token_api/aio/operations_async/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/token_api/aio/operations_async/__pycache__/_bot_sign_in_operations_async.cpython-313.pyc,,
botframework/connector/token_api/aio/operations_async/__pycache__/_user_token_operations_async.cpython-313.pyc,,
botframework/connector/token_api/aio/operations_async/_bot_sign_in_operations_async.py,sha256=ZpYHbb_aOwI9f4JZB55eTcuxD8IWWrCR5WpKVl0bDck,6762
botframework/connector/token_api/aio/operations_async/_user_token_operations_async.py,sha256=t6kDRn8qp8u7XnODK3KWuf43iox3Fp_aTwtMjFg9bec,15012
botframework/connector/token_api/models/__init__.py,sha256=fRqDrq_BK6jya415pOXN1tL0lx_-tbAXtZPjflzHecw,1538
botframework/connector/token_api/models/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/token_api/models/__pycache__/_models.cpython-313.pyc,,
botframework/connector/token_api/models/__pycache__/_models_py3.cpython-313.pyc,,
botframework/connector/token_api/models/_models.py,sha256=3P1UpIXlYY-BtmWYztgP-oBUVapNsrOztZ_V0dFHvPg,7294
botframework/connector/token_api/models/_models_py3.py,sha256=kIAOAzBWwZWy4jzmLWCHrXX3nANPgHQeR6WxHBIQ6pU,7647
botframework/connector/token_api/operations/__init__.py,sha256=REWfD0dloVcvNyCIE7VCduJKYW32HvuTln5lUJgxER8,498
botframework/connector/token_api/operations/__pycache__/__init__.cpython-313.pyc,,
botframework/connector/token_api/operations/__pycache__/_bot_sign_in_operations.cpython-313.pyc,,
botframework/connector/token_api/operations/__pycache__/_user_token_operations.cpython-313.pyc,,
botframework/connector/token_api/operations/_bot_sign_in_operations.py,sha256=AMArA3R-6MrqKYO316pehhgKCSXW32pGNLEUWdPbYAs,6643
botframework/connector/token_api/operations/_user_token_operations.py,sha256=UZjwgok2lKmmbEu36YANJi5LlqZ9M-StH5MwEsQWc8A,14742
botframework/connector/token_api/version.py,sha256=FownKVsSUusgvfaZcBpM2mF1vuuqCG9J0wyvslMZDnY,345
botframework/connector/version.py,sha256=2fJfNcCtZEx_7O6-SKq6X4F_a2FDD_ZhOXX8gJ_Fb5w,342
botframework_connector-4.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
botframework_connector-4.17.0.dist-info/METADATA,sha256=LnQUUBg9mIi8VoRqIHWPCYN2XNsXAb1Dnb568mNC1EE,5906
botframework_connector-4.17.0.dist-info/RECORD,,
botframework_connector-4.17.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
botframework_connector-4.17.0.dist-info/WHEEL,sha256=XRxW4r1PNiVhMpP4bT9oWtu3HyndxpJ84SkubFgzp_Y,109
botframework_connector-4.17.0.dist-info/top_level.txt,sha256=dpCBSDjo9su5dmQYbevD9jc_rxSry_Vb78_gI_wn9Bc,13
