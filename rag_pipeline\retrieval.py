import argparse
import faiss
import numpy as np
import logging
from typing import List, Dict, Any
from langchain_ollama import OllamaEmbeddings, OllamaLLM
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser
from langchain_core.runnables import RunnablePassthrough

from utils.db_utils import get_db_connection, fetch_documents_by_embedding_ids

def load_faiss_index(index_path: str):
    logging.info(f"Loading FAISS index from {index_path}")
    try:
        index = faiss.read_index(index_path)
        logging.info(f"FAISS index loaded with {index.ntotal} vectors of dimension {index.d}")
        return index
    except Exception as e:
        logging.error(f"Failed to load FAISS index: {e}")
        return None

def retrieve_documents(query: str, index, db_config_path: str, top_k: int = 5):
    logging.info(f"\n{'='*50}\nProcessing query: {query}\n{'='*50}")
    
    embedding_model = OllamaEmbeddings(
        model="nomic-embed-text:latest",
        base_url="http://localhost:11434"
    )

    query_embedding = embedding_model.embed_query(query)
    logging.info(f"Query embedding generated with dimension {len(query_embedding)}")

    if index.d != len(query_embedding):
        raise ValueError(f"Embedding dimension mismatch: index expects {index.d}, got {len(query_embedding)}")

    query_embedding_np = np.array([query_embedding]).astype('float32')

    if index.ntotal == 0:
        logging.warning("FAISS index is empty.")
        return []

    distances, indices = index.search(query_embedding_np, top_k)
    logging.info(f"Search complete. Found {len(indices[0])} matches")

    embedding_ids = [int(idx) for idx in indices[0]]
    retrieved_docs = fetch_documents_by_embedding_ids(db_config_path, embedding_ids)

    for i, doc in enumerate(retrieved_docs):
        distance = float(distances[0][i])
        similarity_score = 1 / (1 + distance)  # Convert distance to human-friendly score
        doc["score"] = similarity_score

        logging.info(f"  Match {i+1}: {doc['file_name']} (chunk {doc['chunk_id']}), similarity: {similarity_score:.4f}, raw distance: {distance:.4f}")
        preview = doc["chunk_text"][:100].replace("\n", " ")
        logging.info(f"    Text preview: {preview}...")

    return retrieved_docs

def format_documents(docs: List[Dict[str, Any]]) -> str:
    logging.info("Formatting documents for context")
    context_parts = []

    for i, doc in enumerate(docs):
        meta_lines = [
            f"Document {i+1}",
            f"File: {doc['file_name']}",
            f"Chunk ID: {doc['chunk_id']}",
            f"Page Number: {doc.get('page_number', 'N/A')}",
            f"Similarity Score: {doc['score']:.4f}",
            "-" * 40,
            doc['chunk_text']
        ]
        context_part = "\n".join(meta_lines) + "\n"
        context_parts.append(context_part)
        logging.info(f"Added document {i+1} to context ({len(doc['chunk_text'])} chars)")

    context = "\n\n".join(context_parts)
    logging.info(f"Total context length: {len(context)} characters")
    return context

def format_sources(docs: List[Dict[str, Any]]) -> str:
    sources = ["\nSources:"]
    for doc in docs:
        file_name = doc.get("file_name", "unknown")
        chunk_id = doc.get("chunk_id", "N/A")
        page_number = doc.get("page_number", -1)

        source_line = f"- {file_name}, chunk {chunk_id}"
        if page_number != -1:
            source_line += f", page {page_number}"
        
        sources.append(source_line)
    return "\n".join(sources)

def answer_question(query: str, context: str, model_name: str = "deepseek-r1:8b"):
    logging.info(f"\nGenerating answer with model: {model_name}")
    
    llm = OllamaLLM(
        model=model_name,
        base_url="http://localhost:11434",
        temperature=0
    )

    prompt = ChatPromptTemplate.from_template("""
    You are a helpful assistant that answers questions based on the provided context.

    Context:
    {context}

    Question: {question}

    Answer the question based only on the provided context. If the context doesn't contain 
    the information needed to answer the question, say "I don't have enough information to 
    answer this question." Be concise and accurate.
    """)

    chain = (
        {"context": RunnablePassthrough(), "question": lambda _: query}
        | prompt
        | llm
        | StrOutputParser()
    )

    logging.info("Sending query to LLM...")
    response = chain.invoke(context)
    logging.info("Response received from LLM")

    return response

def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser(description="RAG Retrieval System")
    parser.add_argument("--query", type=str, required=True, help="Query to search for")
    parser.add_argument("--db_config", type=str, required=True, help="Path to database config JSON file")
    parser.add_argument("--faiss_index", type=str, required=True, help="Path to FAISS index file")
    parser.add_argument("--top_k", type=int, default=5, help="Number of documents to retrieve")
    parser.add_argument("--llm_model", type=str, default="deepseek-r1:8b", 
                        help="LLM model to use (e.g., deepseek-r1:8b, deepseek-v2:16b)")
    args = parser.parse_args()

    index = load_faiss_index(args.faiss_index)
    if index is None:
        logging.error("Failed to load FAISS index. Exiting.")
        return

    try:
        docs = retrieve_documents(args.query, index, args.db_config, args.top_k)

        if not docs:
            logging.info("No relevant documents found.")
            return

        context = format_documents(docs)
        answer = answer_question(args.query, context, args.llm_model)

        logging.info("\n" + "="*80)
        logging.info("QUERY: " + args.query)
        logging.info("-"*80)
        logging.info("ANSWER:")
        logging.info(answer)

        sources = format_sources(docs)
        logging.info(sources)

        logging.info("="*80)

    except Exception as e:
        logging.error(f"Error: {e}")

if __name__ == "__main__":
    main()
